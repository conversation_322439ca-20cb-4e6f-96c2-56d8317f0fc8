package com.magnamedia.controller;

import com.magnamedia.core.Setup;
import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.entity.Parameter;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.entity.PushNotification;
import com.magnamedia.core.entity.Template;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.helper.TemplateUtil;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.core.repository.ParameterRepository;
import com.magnamedia.core.repository.TemplateRepository;
import com.magnamedia.core.repository.template.ChannelSpecificSettingRepository;
import com.magnamedia.core.type.NotificationLocation;
import com.magnamedia.core.type.template.ChannelSpecificSettingType;
import com.magnamedia.entity.Contract;
import com.magnamedia.entity.DDBankMessaging;
import com.magnamedia.entity.DDMessaging;
import com.magnamedia.entity.DDMessagingContract;
import com.magnamedia.entity.projection.DDMessagingListProjection;
import com.magnamedia.entity.workflow.FlowEventConfig;
import com.magnamedia.extra.*;
import com.magnamedia.helper.PicklistHelper;
import com.magnamedia.module.type.*;
import com.magnamedia.repository.*;
import com.magnamedia.service.AccountingTemplateService;
import com.magnamedia.service.DDMessagingService;
import com.magnamedia.service.FlowProcessorMessagingService;
import com.magnamedia.service.MessagingService;
import com.sun.istack.logging.Logger;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.projection.ProjectionFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

import static com.magnamedia.module.AccountingModule.PARAMETER_BOUNCED_MAX_TRIALS_FOR_CC;
import static com.magnamedia.module.AccountingModule.PARAMETER_BOUNCED_MAX_TRIALS_FOR_MV;

/**
 * <AUTHOR>
 * Created on Apr 13, 2020
 * ACC-1611
 */
@RequestMapping("/DDMessaging")
@RestController
public class DDMessagingController extends BaseRepositoryController<DDMessaging> {
    private final Logger logger = Logger.getLogger(DDMessagingController.class);

    @Autowired
    private TemplateUtil templateUtil;

    @Autowired
    private DDMessagingRepository ddMessagingRepository;

    @Autowired
    private ProjectionFactory projectionFactory;

    @Autowired
    private TemplateRepository templateRepository;

    @Autowired
    private DDMessagingService ddMessagingService;
    @Autowired
    private AccountingTemplateService accountingTemplateService;

    @Override
    public BaseRepository<DDMessaging> getRepository() {
        return ddMessagingRepository;
    }


    @Override
    protected ResponseEntity<?> createEntity(DDMessaging entity) {

        if (!ddMessagingService.validateCreateClientToDo(entity)) {
            return ResponseEntity.ok("The defined event does not match any predefined client to-do.");
        }

        Long id = entity.checkEntity();
        if (id != null) {
            Map<String, Object> response = new HashMap<>();
            response.put("message", "A message with the same conditions already exist");
            response.put("isDuplicated", true);
            response.put("id", id);
            return new ResponseEntity<>(response, HttpStatus.OK);
        }

        return super.createEntity(entity);
    }

    @Override
    protected ResponseEntity<?> updateEntity(DDMessaging entity) {

        if (!ddMessagingService.validateCreateClientToDo(entity)) {
            return ResponseEntity.ok("The defined event does not match any predefined client to-do.");
        }

        Long id = entity.checkEntity();
        if (id != null) {
            Map<String, Object> response = new HashMap<>();
            response.put("message", "A message with the same conditions already exist");
            response.put("isDuplicated", true);
            response.put("id", id);
            return new ResponseEntity<>(response, HttpStatus.OK);
        }

        return super.updateEntity(entity);
    }

    @PreAuthorize("hasPermission('DDMessaging','updateTemplatesChannels')")
    @PostMapping("/updateTemplatesChannels/{id}")
    public ResponseEntity<?> updateTemplatesChannels(
            @PathVariable("id") DDMessaging ddMessaging,
            @RequestBody Map<String, String> body)  {

        Template t = ddMessaging.getClientTemplate();

        if (t.isChannelExist(ChannelSpecificSettingType.SMS) && body.containsKey("smsChannelText")) {
            t.getChannelSetting(ChannelSpecificSettingType.SMS.toString()).setText(body.get("smsChannelText"));
        }

        if (t.isChannelExist(ChannelSpecificSettingType.Notification) && body.containsKey("notificationChannelText")) {
            t.getChannelSetting(ChannelSpecificSettingType.Notification.toString()).setText(body.get("notificationChannelText"));
        }

        TemplateUtil.updateTemplate(t);
        return new ResponseEntity<>("Done", HttpStatus.OK);
    }

    @Override
    public ResponseEntity<?> list(Pageable pageable) {
        SelectQuery query = new SelectQuery<>(DDMessaging.class);
        query.sortBy("event", true);
        return new ResponseEntity(query.execute(pageable).map(dd -> projectionFactory.createProjection(
                DDMessagingListProjection.class, dd)), HttpStatus.OK);
    }


    @PreAuthorize("hasPermission('DDMessaging','advanceSearch')")
    @PostMapping(value = "/advanceSearch")
    public ResponseEntity<?> advanceSearch(
            @RequestParam(name = "event", required = false) DDMessagingType event,
            @RequestParam(name = "contractType", required = false) String contractType,
            @RequestParam(name = "ddCategory", required = false) String ddCategory,
            @RequestParam(name = "rejectCategory", required = false) DirectDebitRejectCategory rejectCategory,
            @RequestParam(name = "dataEntryRejectCategory", required = false) DirectDebitDataEntryRejectCategory dataEntryRejectCategory,
            @RequestParam(name = "bouncedPaymentStatus", required = false) PicklistItem bouncedPaymentStatus,
            @RequestParam(name = "scheduleTermCategory", required = false) DirectDebitMessagingScheduleTermCategory scheduleTermCategory,
            @RequestParam(name = "trial", required = false) String trial,
            @RequestParam(name = "reminder", required = false) String reminder,
            @RequestParam(name = "messageID", required = false) Long messageID,
            @RequestParam(name = "subType", required = false) DDMessagingSubType subType,
            @RequestParam(name = "paymentStructure", required = false) DDMessagingPaymentStructure paymentStructure,
            Pageable pageable) {

        SelectQuery query = new SelectQuery<>(DDMessaging.class);
        if (event != null) {
            query.filterBy("event", "=", event);
        }
        if (contractType != null) {
            query.filterBy("contractProspectTypes", "like", "%" + contractType + "%");
        }
        if (ddCategory != null && !ddCategory.isEmpty()) {
            query.filterBy("ddCategory", "like", "%" + ddCategory + "%");
        }
        if (rejectCategory != null) {
            query.filterBy("rejectCategory", "=", rejectCategory);
        }
        if (dataEntryRejectCategory != null) {
            query.filterBy("dataEntryRejectCategory", "=", dataEntryRejectCategory);
        }
        if (bouncedPaymentStatus != null) {
            query.filterBy("bouncedPaymentStatus", "=", bouncedPaymentStatus);
        }
        if (scheduleTermCategory != null) {
            query.filterBy("scheduleTermCategory", "=", scheduleTermCategory);
        }
        if (trial != null && !trial.isEmpty()) {
            query.filterBy("trials", "like", "%" + trial + "%");
        }
        if (reminder != null && !reminder.isEmpty()) {
            query.filterBy("reminders", "like", "%" + reminder + "%");
        }
        if (messageID != null) {
            query.filterBy("id", "=",  messageID );
        }
        if (subType != null) {
            query.filterBy("subType", "=",  subType );
        }

        if (paymentStructure != null) {
            query.filterBy("paymentStructure", "=", paymentStructure);
        }

        query.sortBy("event", true);

        return new ResponseEntity(query.execute(pageable).map(dd -> projectionFactory.createProjection(
                DDMessagingListProjection.class, dd)), HttpStatus.OK);
    }


    @PreAuthorize("hasPermission('transactions','data-correction')")
    @RequestMapping(path = "/data-migration/cc-notification", method = RequestMethod.GET)
    public ResponseEntity createNotificationsTemplates() {
        int index = 0;
        Page<DDMessaging> transactionPage;
        do {
            transactionPage = ddMessagingRepository.findByClientTemplateIsNotNull(PageRequest.of(index++, 100));

            for (DDMessaging ddMessaging : transactionPage.getContent()) {

                Template clientTemplate = ddMessaging.getClientTemplate();

                clientTemplate = templateRepository.findOne(clientTemplate.getId());

                Template smsTemplate = new Template("", "", "", "");

                BeanUtils.copyProperties(clientTemplate, smsTemplate);

                smsTemplate.setId(null);

                String smsTemplateUniqueName = "Accounting_dd_messaging_setup_client_" + ddMessaging.getEvent().toString();

                Template smsByNameIgnoreCase = templateRepository.findByNameIgnoreCase(smsTemplateUniqueName);
                int y = 0;
                while (smsByNameIgnoreCase != null) {
                    y++;
                    smsByNameIgnoreCase = templateRepository.findByNameIgnoreCase(smsTemplateUniqueName + y);
                }
                smsTemplate.setName(y == 0 ? (smsTemplateUniqueName) : smsTemplateUniqueName + y);
                TemplateUtil.createTemplate(smsTemplate, new HashMap<>());

                clientTemplate.setNotificationSmsTemplateName(smsTemplate.getName());
                clientTemplate.setDescription("this is a notification template that is linked with sms template:" + smsTemplate.getId());

                templateRepository.save(clientTemplate);

            }
        } while (transactionPage.hasNext());

        return new ResponseEntity("Done", HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('DDMessaging','updateNotificationsAcc4689')")
    @PostMapping("/updateNotificationsAcc4689")
    public ResponseEntity<?> updateNotificationsAcc4689(MultipartFile file) throws IOException {
        XSSFWorkbook workbook = new XSSFWorkbook(file.getInputStream());

        ddMessagingService.updateNotificationsAcc4689ReadSheetData(
                workbook.getSheet("Priority 1 originally"));
        ddMessagingService.updateNotificationsAcc4689ReadSheetData(
                workbook.getSheet("Priority 2 originally"));

        return okResponse();
    }

    // ACC-4837
    @PreAuthorize("hasPermission('DDMessaging','migrateACC4837')")
    @PostMapping("/migrateACC4837")
    public ResponseEntity<?> migrateACC4837(){

        List<DirectDebitRejectCategory> categories = Arrays.asList(DirectDebitRejectCategory.Signature,
                DirectDebitRejectCategory.EID,
                DirectDebitRejectCategory.Account,
                DirectDebitRejectCategory.Invalid_Account);

        SelectQuery<DDMessaging> query = new SelectQuery<>(DDMessaging.class);
        query.filterBy("event", "=", DDMessagingType.DirectDebitRejected);
        query.filterBy("rejectCategory", "in", categories);
        List<DDMessaging> ddMessagingList = query.execute();

        for(DirectDebitRejectCategory category : categories){
            ddMessagingService.processMigrationACC4837(ddMessagingList, category);
        }

        return okResponse();
    }

    @PreAuthorize("hasPermission('DDMessaging','migrateACC4603')")
    @PostMapping(path = "/migrateACC4603")
    public ResponseEntity<?> migrateACC4603(){
        SelectQuery<DDMessaging> query = new SelectQuery<>(DDMessaging.class);
        query.filterBy("event", "=", DDMessagingType.BouncedPayment)
                .and("contractProspectTypes", "=", "maidvisa.ae_prospect")
                .and("bouncedPaymentStatus.code", "=", "has_e-signature_and_manual_dd");
        query.sortBy("trials", true);
        List<DDMessaging> ddMessagingList = query.execute();

        if(ddMessagingList.isEmpty()) throw new RuntimeException("No Results");
        logger.info("number of records = " + ddMessagingList.size());

        boolean firstRun = ddMessagingList.stream()
                .noneMatch(d -> d.getTrials().equals("6"));

        for (DDMessaging ddMessaging : ddMessagingList) {
            ddMessagingService.updateDDMessagingACC4603(ddMessaging, firstRun);
        }

        for(int trials=3; trials <= 5 && firstRun ; trials++){
           ddMessagingService.createDDMessagingACC4603(trials);
        }

        return okResponse();
    }

    @PreAuthorize("hasPermission('DDMessaging','updateNotificationsACC5214')")
    @PostMapping("/updateNotificationsACC5214")
    public ResponseEntity<?> updateNotificationsACC5214(MultipartFile file) throws IOException {
        ddMessagingService.updateNotificationsACC5214ReadSheetData(
                file, "Incomplete",  true, "maidvisa.ae_prospect");
        ddMessagingService.updateNotificationsACC5214ReadSheetData(
                file ,"Invalid Account Rejection",  true, "maidvisa.ae_prospect");
        ddMessagingService.updateNotificationsACC5214ReadSheetData(
                file, "Title Mismatch",  true, "maidvisa.ae_prospect");
        ddMessagingService.updateNotificationsACC5214ReadSheetData(
                file, "Wrong EID",  true, "maidvisa.ae_prospect");
        ddMessagingService.updateNotificationsACC5214ReadSheetData(
                file, "Authorization",  true, "maidvisa.ae_prospect");
        ddMessagingService.updateNotificationsACC5214ReadSheetData(
                file, "Signature Rejection",  true, "maidvisa.ae_prospect");
        ddMessagingService.updateNotificationsACC5214ReadSheetData(
                file, "Bounced Payments",  true, "maidvisa.ae_prospect");

        TemplateUtil templateUtil = Setup.getApplicationContext().getBean(TemplateUtil.class);

        // Payment Expiry sheet
        {
            Template t411Notification =
                    TemplateUtil.getTemplate(MvNotificationTemplateCode.MV_PAYMENT_EXPIRY_4_1_1_NOTIFICATION.toString());
            t411Notification.setText("ACTION REQUIRED: The Monthly Bank Payment Form that you’ve previously signed expires soon. " +
                    "To avoid interruption in your service, we'll amend and resubmit your Bank Payment Forms to " +
                    "the bank. Please remember, you can stop the service and cancel your contract anytime you want. If you have " +
                    "any questions, please @chat_with_us@ .");
            t411Notification.setNotificationAlwaysSendSms(false);
            t411Notification.setNotificationHoursBeforeSendSms(null);
            t411Notification.setNotificationCanClosedByUser(true);
            t411Notification.setNotificationLocation(NotificationLocation.HOME);
            templateUtil.updateTemplate(t411Notification, new HashMap<>());
        }

        {
            Template t411Sms = TemplateUtil.getTemplate(MvSmsTemplateCode.MV_PAYMENT_EXPIRY_4_1_1_SMS.toString());
            t411Sms.setText("ACTION REQUIRED: The Monthly Bank Payment Form that you’ve previously signed " +
                    "expires soon. To avoid interruption in your service, we'll amend and resubmit your Bank Payment " +
                    "Forms to the bank. Please remember, you can stop the service and cancel your contract " +
                    "anytime you want. If you have any questions, please reach us at: @chat_with_us_link@");
            templateUtil.updateTemplate(t411Sms, new HashMap<>());
        }

        {
            Template t412Notification =
                    TemplateUtil.getTemplate(MvNotificationTemplateCode.MV_PAYMENT_EXPIRY_4_1_2_NOTIFICATION.toString());
            t412Notification.setText("The Monthly Bank Payment Form that you’ve previously signed expires on " +
                    "@ExpiryDate@. To avoid interruption in your service, please @link_send_dd_details_click_here@ to sign the new payment form.");
            t412Notification.setNotificationAlwaysSendSms(false);
            t412Notification.setNotificationHoursBeforeSendSms(null);
            t412Notification.setNotificationCanClosedByUser(true);
            t412Notification.setNotificationLocation(NotificationLocation.HOME);;
            templateUtil.updateTemplate(t412Notification, new HashMap<>());
        }

        {
            Template t412Sms = TemplateUtil.getTemplate(MvSmsTemplateCode.MV_PAYMENT_EXPIRY_4_1_2_SMS.toString());
            t412Sms.setText("@greetings@ The Monthly Bank Payment Form that you’ve previously signed expires on @ExpiryDate@. " +
                    "To avoid interruption in your service, please click here to sign the new payment form: " +
                    "@link_send_dd_details@ .");
            templateUtil.updateTemplate(t412Sms, new HashMap<>());
        }

        {
            Template t412Sms = TemplateUtil.getTemplate(MvNotificationTemplateCode.MV_DD_PENDING_INFO_NOTIFICATION.toString());
            t412Sms.setText("ACTION REQUIRED: Please @link_send_dd_details_click_here@ , and complete your Bank Payment Form.");
            templateUtil.updateTemplate(t412Sms, new HashMap<>());
        }

        // IPAM
        SelectQuery<DDMessaging> query = new SelectQuery<>(DDMessaging.class);
        query.filterBy("event", "=", DDMessagingType.ClientPaidCashAndNoSignatureProvided);
        query.filterBy("contractProspectTypes", "like", "%maidvisa.ae_prospect%");

        Setup.getApplicationContext().getBean(FlowProcessorController.class)
                .updateNoSignatureDdMessaging(query.execute());
        Setup.getApplicationContext().getBean(FlowProcessorController.class)
                .updateNoSignatureDdMessagingMvDirectlyOnContactCreation(query.execute()
                        .stream().filter(d -> d.getSubType()
                                .equals(DDMessagingSubType.NO_SIGNATURE_WITH_ONE_PAYMENT_ONLY))
                        .collect(Collectors.toList()).get(0));

        // online reminder
        Setup.getApplicationContext().getBean(FlowProcessorController.class)
                .OnlinePaymentsFlowNotificationsSetupMv();

        // bounced flow create expert todo
        query = new SelectQuery<>(DDMessaging.class);
        query.filterBy("event", "=", DDMessagingType.BouncedPayment);
        query.execute().forEach(d -> {
            boolean isTrial4AndHasESignatureAndManualDd = d.getBouncedPaymentStatus() != null
                && d.getBouncedPaymentStatus().getCode().equals("has_e-signature_and_manual_dd")
                && d.getTrials().equals("4");
            boolean isFirstThreeReminderInHasNoESignature = d.getBouncedPaymentStatus() != null
                && d.getBouncedPaymentStatus().getCode().equals("has_no_e-signature")
                && d.getTrials().equals("0")
                && Arrays.asList("2", "3", "4").contains(d.getReminders())
                && d.getContractProspectTypes().contains("maidvisa.ae_prospect");
            boolean thirdTrialInHasESSignatureAndNoManualDd = d.getBouncedPaymentStatus() != null
                && d.getBouncedPaymentStatus().getCode().equals("has_e-signature_and_no_manual_dd")
                && d.getTrials().equals("3")
                && d.getReminders().equals("0");
            boolean ccByIds = Arrays.asList("87", "88", "95", "96", "97")
                    .contains(d.getId().toString());
            d.setCreateHumanSms(isTrial4AndHasESignatureAndManualDd ||
                    isFirstThreeReminderInHasNoESignature ||
                    thirdTrialInHasESSignatureAndNoManualDd ||
                    ccByIds);
            ddMessagingRepository.save(d);
        });

        return new ResponseEntity<>("Done", HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('DDMessaging','updateNotificationsACC5235')")
    @PostMapping("/updateNotificationsACC5235")
    public ResponseEntity<?> updateNotificationsACC5235(MultipartFile file) throws IOException {
        ddMessagingService.updateNotificationsACC5214ReadSheetData(
                file, "Incomplete DD", true, "maids.cc_prospect");
        ddMessagingService.updateNotificationsACC5214ReadSheetData(
                file, "Signature rejected", true, "maids.cc_prospect");
        ddMessagingService.updateNotificationsACC5214ReadSheetData(
                file, "Wrong EID", true, "maids.cc_prospect");
        ddMessagingService.updateNotificationsACC5214ReadSheetData(
                file, "Authorization", true, "maids.cc_prospect");
        ddMessagingService.updateNotificationsACC5214ReadSheetData(
                file, "Bounced payments", true, "maids.cc_prospect");
        ddMessagingService.updateNotificationsACC5214ReadSheetData(
                file, "missing signature flow", true, "maids.cc_prospect");
        ddMessagingService.updateNotificationsACC5214ReadSheetData(
                file, "Title Mismatch", true, "maids.cc_prospect");
        ddMessagingService.updateNotificationsACC5214ReadSheetData(
                file, "Invalid Account Rejection", true, "maids.cc_prospect");


        TemplateUtil templateUtil = Setup.getApplicationContext().getBean(TemplateUtil.class);

        // bounced payment row 14
        {
            Template t = TemplateUtil.getTemplate("Accounting_dd_messaging_setup_clientBouncedPayment29");
            t.setNotificationCanClosedByUser(true);
            t.setNotificationLocation(NotificationLocation.HOME);
            templateUtil.updateTemplate(t, new HashMap<>());
        }

        // Payment Expiry sheet
        {
            Template t411Notification =
                    TemplateUtil.getTemplate(CcNotificationTemplateCode.CC_PAYMENT_EXPIRY_4_1_1_NOTIFICATION.toString());
            t411Notification.setText("The Bank Payment Form that you’ve previously signed expires soon. To avoid interruptions " +
                    "in your service, we'll amend and resubmit your Bank Payment Forms to the bank. Please remember, you can " +
                    "stop the service and cancel your Banka Payment Forms at anytime by returning the maid to our center. If " +
                    "you have any questions, please WhatsApp us @chat_with_us@ .");
            t411Notification.setSendSMSIfNotReceived(true);
            t411Notification.setNotificationHoursBeforeSendSms(2);
            t411Notification.setNotificationCanClosedByUser(true);
            t411Notification.setNotificationLocation(NotificationLocation.HOME);
            templateUtil.updateTemplate(t411Notification, new HashMap<>());
        }

        {
            Template t411Sms = TemplateUtil.getTemplate(CcSmsTemplateCode.CC_PAYMENT_EXPIRY_4_1_1_SMS.toString());
            t411Sms.setText("The Bank Payment Form that you’ve previously signed expires soon.To avoid interruptions " +
                    "in your service, we'll amend and resubmit your Bank Payment Forms to the bank. " +
                    "Please remember, you can stop the service and cancel your Bank Payment Forms at anytime by " +
                    "returning the maid to our center. If you have any questions, please reach us at: " +
                    "@chat_with_us_link@ .");
            templateUtil.updateTemplate(t411Sms, new HashMap<>());
        }

        {
            Template t412Notification =
                    TemplateUtil.getTemplate(CcNotificationTemplateCode.CC_PAYMENT_EXPIRY_4_1_2_NOTIFICATION.toString());
            t412Notification.setText("The Bank Payment Form that you signed expires on @ExpiryDate@. To continue your service, " +
                    "please @link_send_dd_details_click_here@ to sign the new Bank Payment Form. Your monthly payments will remain AED " +
                    "@monthly_fee_of_nationality@ per month.");
            t412Notification.setNotificationAlwaysSendSms(false);
            t412Notification.setNotificationHoursBeforeSendSms(2);
            t412Notification.setSendSMSIfNotReceived(true);
            t412Notification.setNotificationLocation(NotificationLocation.HOME);
            templateUtil.updateTemplate(t412Notification, new HashMap<>());
        }

        {
            Template t412Sms = TemplateUtil.getTemplate(CcSmsTemplateCode.CC_PAYMENT_EXPIRY_4_1_2_SMS.toString());
            t412Sms.setText("The Bank Payment Form that you signed expires on @ExpiryDate@. " +
                    "To continue your service, please click here to sign the new Bank Payment Form: @link_send_dd_details@ ." +
                    "Your monthly payments will remain AED @monthly_fee_of_nationality@ per month.");
            templateUtil.updateTemplate(t412Sms, new HashMap<>());
        }

        // Sending the client to sign
        {
            Template t = TemplateUtil.getTemplate(CcNotificationTemplateCode.CC_DD_PENDING_INFO_NOTIFICATION.toString());
            t.setText("ACTION REQUIRED: Please @link_send_dd_details_click_here@ , and complete your Bank Payment Form application.");
            t.setNotificationHoursBeforeSendSms(2);
            t.setNotificationAlwaysSendSms(false);
            t.setSendSMSIfNotReceived(true);
            t.setNotificationLocation(NotificationLocation.HOME);
            templateUtil.updateTemplate(t, new HashMap<>());
        }

        {
            Template t = TemplateUtil.getTemplate(CcSmsTemplateCode.CC_DD_PENDING_INFO_SMS.toString());
            t.setText("@greetings@, please click on the following link @link_send_dd_details@ using your phone, " +
                    "and complete your Bank Payment Form.");
            templateUtil.updateTemplate(t, new HashMap<>());
        }

        // sub flows
        {
            Template t = TemplateUtil.getTemplate(CcNotificationTemplateCode.CC_ACCOUNTING_NOT_OWED_MONEY_FROM_CLIENT_8_1_2_NOTIFICATION.toString());
            t.setText("Unfortunately, the bank didn't process the cancellation of your future payments on time. " +
                    "You were charged AED @amount@. Don't worry; we’ll send you that amount within the next 7 business days.");
            t.setNotificationAlwaysSendSms(false);
            t.setNotificationCanClosedByUser(true);
            t.setNotificationHoursBeforeSendSms(null);
            t.setNotificationLocation(NotificationLocation.HOME);
            templateUtil.updateTemplate(t, new HashMap<>());
        }

        {
            Template t = TemplateUtil.getTemplate(CcSmsTemplateCode.CC_ACCOUNTING_NOT_OWED_MONEY_FROM_CLIENT_8_1_2_SMS.toString());
            t.setText("Unfortunately, the bank didn't process the cancellation of your future payments on time. " +
                    "You were charged AED @amount@. Don't worry; we’ll send you that amount within the next 7 business days.");
            templateUtil.updateTemplate(t, new HashMap<>());
        }

        {
            Template t = TemplateUtil.getTemplate(CcNotificationTemplateCode.CC_ACCOUNTING_WRONGLY_CHARGED_MONEY_ON_CLIENT_8_1_3_NOTIFICATION.toString());
            t.setText("You were charged AED @amount@ today. But don’t worry, we’ll transfer you the same amount by " +
                    "@scheduled_termination_date@.");
            t.setNotificationAlwaysSendSms(false);
            t.setNotificationCanClosedByUser(true);
            t.setNotificationHoursBeforeSendSms(null);
            t.setNotificationLocation(NotificationLocation.HOME);
            ;
            templateUtil.updateTemplate(t, new HashMap<>());
        }

        {
            Template t = TemplateUtil.getTemplate(CcSmsTemplateCode.CC_ACCOUNTING_WRONGLY_CHARGED_MONEY_ON_CLIENT_8_1_3_SMS.toString());
            t.setText("You were charged AED @amount@ today. But don’t worry, we’ll transfer you the same amount by " +
                    "@scheduled_termination_date@.");
            templateUtil.updateTemplate(t, new HashMap<>());
        }

        {
            Template t = TemplateUtil.getTemplate(CcNotificationTemplateCode.CC_ACCOUNTING_PAY_VIA_CREDIT_CARD_NOTIFICATION.toString());
            t.setText("ACTION REQUIRED:\nTo pay your overdue payment of AED @amount@ via credit card, please @paytab_link_click_here@ .");
            templateUtil.updateTemplate(t, new HashMap<>());
        }

        {
            Template t = TemplateUtil.getTemplate(CcSmsTemplateCode.CC_CLIENT_REFUND_BANK_TRANSFER_DETAILS_SMS.toString());
            t.setText("We just sent you AED @amount@ by bank transfer. Please expect to receive the amount to your bank account within 7 " +
                    "business days. Click here to view the proof of transfer: @proof_of_transfer_link@ . Thank you");
            templateUtil.updateTemplate(t, new HashMap<>());
        }

        // bank transfer
        {
            Template t = TemplateUtil.getTemplate(CcNotificationTemplateCode.CC_CLIENT_REFUND_BANK_TRANSFER_DETAILS_NOTIFICATION.toString());
            t.setText("We just sent you AED @amount@ by bank transfer. Please expect to receive the amount to your bank account within 7 business days." +
                    " @proof_of_transfer_link_Click_here@ to view the proof of transfer. Thank you.");
            t.setNotificationAlwaysSendSms(false);
            t.setNotificationCanClosedByUser(true);
            t.setNotificationHoursBeforeSendSms(null);
            t.setNotificationLocation(NotificationLocation.HOME);
            templateUtil.updateTemplate(t, new HashMap<>());
        }

        {
            Template t = TemplateUtil.getTemplate(CcSmsTemplateCode.CC_CLIENT_REFUND_BANK_TRANSFER_DETAILS_SMS.toString());
            t.setText("We just sent you AED @amount@ by bank transfer. Please expect to receive the amount to your bank account within 7 business days. Click here to view the proof of transfer: " +
                    "@proof_of_transfer_link@. Thank you.");
            templateUtil.updateTemplate(t, new HashMap<>());
        }

        // IPAM
        SelectQuery<DDMessaging> query = new SelectQuery<>(DDMessaging.class);
        query.filterBy("event", "=", DDMessagingType.ClientPaidCashAndNoSignatureProvided);
        query.filterBy("contractProspectTypes", "like", "%maids.cc_prospect%");

        Setup.getApplicationContext().getBean(FlowProcessorController.class)
                .updateNoSignatureDdMessaging(query.execute());
        Setup.getApplicationContext().getBean(FlowProcessorController.class)
                .updateNoSignatureDdMessagingCcDirectlyOnContactCreation(query.execute()
                        .stream().filter(d -> d.getSubType()
                                .equals(DDMessagingSubType.NO_SIGNATURE_WITH_ONE_PAYMENT_ONLY))
                        .collect(Collectors.toList()).get(0));

        // online reminder
        Setup.getApplicationContext().getBean(FlowProcessorController.class)
                .OnlinePaymentsFlowNotificationsSetupCc();

        return new ResponseEntity<>("Done", HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('DDMessaging','getDdMessagingEvent')")
    @GetMapping("/getDdMessagingEvent")
    public ResponseEntity<?> getDdMessagingEvent() {
        Map<String, String> map = new HashMap<>();
        DDMessagingType [] array = DDMessagingType.values();
        for (DDMessagingType d: array) {
            if (d.isDeprecated()) continue;
            map.put(d.getValue(), d.getLabel());
        }
        return new ResponseEntity<>(map, HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('DDMessaging','getDdMessagingPaymentStructure')")
    @GetMapping("/getDdMessagingPaymentStructure")
    public ResponseEntity<?> getDdMessagingPaymentStructure() {

        return ResponseEntity.ok(DDMessagingPaymentStructure.values());
    }

    @PreAuthorize("hasPermission('DDMessaging','getDdMessagingSubEvent')")
    @GetMapping("/getDdMessagingSubEvent")
    public ResponseEntity<?> getDdMessagingSubEvent(@RequestParam(name = "event") DDMessagingType event) {
        SelectQuery<FlowEventConfig> query = new SelectQuery<>(FlowEventConfig.class);
        List<FlowEventConfig> flowEventConfigs = query.execute().stream()
                .filter(flowEventConfig -> flowEventConfig.getName().getMessagingType().equals(event))
                .collect(Collectors.toList());

        Map<String, String> map = new HashMap<>();
        if (flowEventConfigs.isEmpty())
            return new ResponseEntity<>(map, HttpStatus.OK);
        FlowEventConfig flowEventConfig = flowEventConfigs.get(0);

        flowEventConfig.getSubEventConfigs().forEach(f -> {
            if (f.getName().isDeprecated()) return;

            map.put(f.getName().getDdMessagingSubType().getValue(), f.getName().getDdMessagingSubType().getLabel());
        });
        return new ResponseEntity<>(map, HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('DDMessaging','updateNotificationsCMA3901')")
    @PostMapping("/updateNotificationsCMA3901")
    public ResponseEntity<?> updateNotificationsCMA3901(
            MultipartFile file) throws IOException {

        ddMessagingService.updateNotificationsCMA3901ReadSheetData(file, "cms");

        return new ResponseEntity<>("Done", HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('DDMessaging','updateNotificationsACC6064')")
    @PostMapping("/updateNotifications6064")
    public ResponseEntity<?> updateNotificationsACC6064(
            MultipartFile file) throws IOException {

        ParameterRepository parameterRepository = Setup.getRepository(ParameterRepository.class);
        Parameter cc = parameterRepository.findByModuleAndCode(Setup.getCurrentModule(), PARAMETER_BOUNCED_MAX_TRIALS_FOR_CC);
        Parameter mv = parameterRepository.findByModuleAndCode(Setup.getCurrentModule(), PARAMETER_BOUNCED_MAX_TRIALS_FOR_MV);

        ddMessagingService.updateNotificationsACC6064ReadSheetData(
                file, "cc", !cc.getValue().equals("4"));
        ddMessagingService.updateNotificationsACC6064ReadSheetData(
                file, "mv", !cc.getValue().equals("4"));

        if (!cc.getValue().equals("4")) {
            cc.setValue("4");
            parameterRepository.save(cc);
            mv.setValue("7");
            parameterRepository.save(mv);
        }

        return new ResponseEntity<>("Done", HttpStatus.OK);
    }

    // ACC-5498
    @PreAuthorize("hasPermission('DDMessaging','updateNotificationsACC5498')")
    @GetMapping("/updateNotificationsACC5498")
    public ResponseEntity<?> updateNotificationsACC5498()  {

        PicklistItem p = PicklistHelper.getItem(
                "template_channel_trailing_sentence", "trailing_phrases_link_to_cc_app");
        ChannelSpecificSettingRepository r = Setup.getRepository(ChannelSpecificSettingRepository.class);

        Setup.getRepository(AccTemplateRepository.class)
                .findTemplatesAcc5498()
                .forEach(s -> {
                    s.setTrailingSentence(p);
                    r.save(s);
                });

        return new ResponseEntity<>("Done", HttpStatus.OK);
    }

    // ACC-5577 ACC-4837
    @PreAuthorize("hasPermission('DDMessaging','updateNotificationACC5577')")
    @GetMapping("/updateNotificationACC5577")
    public ResponseEntity<?> updateNotificationACC5577() {

        {
            // CC DirectDebitRejected Signature trial 1 reminder 2
            Template t = TemplateUtil.getTemplate("Accounting_dd_messaging_setup_client_notification_DirectDebitRejected_142");
            t.getChannelSetting(ChannelSpecificSettingType.Notification.toString())
                    .setText("We're sure your maid is very valuable to you and your family. Unfortunately, " +
                    "if you don't complete the Bank Payment Form today, our system will automatically cancel your " +
                    "service and notify your maid to come back to our accommodation. To continue your service," +
                    " please @link_send_dd_details_click_here@ and sign.");
            templateUtil.updateTemplate(t, new HashMap<>());
        }

        {
            // CC DirectDebitRejected Signature trial 2 reminder 2
            Template t = TemplateUtil.getTemplate("Accounting_dd_messaging_setup_client_notification_DirectDebitRejected_143");
            t.getChannelSetting(ChannelSpecificSettingType.Notification.toString())
                    .setText("We're sure your maid is very valuable to you and your family. Unfortunately, " +
                    "if you don't complete the Bank Payment Form today, our system will automatically cancel your " +
                    "service and notify your maid to come back to our accommodation. To continue your service," +
                    " please @link_send_dd_details_click_here@ and sign.");
            templateUtil.updateTemplate(t, new HashMap<>());
        }

        {
            // CC DirectDebitRejected Signature trial 3 reminder 2
            Template t = TemplateUtil.getTemplate("Accounting_dd_messaging_setup_client_notification_DirectDebitRejected_144");
            t.getChannelSetting(ChannelSpecificSettingType.Notification.toString())
                    .setText("We're sure your maid is very valuable to you and your family. Unfortunately, if you don't " +
                    "complete the Bank Payment Form today, our system will automatically cancel your service and notify " +
                    "your maid to come back to our accommodation. To continue your service. Please, would you take a " +
                    "moment to sign again right now on a piece of white paper and send us a photo of the paper by @link_send_dd_details_clicking_here@ .");
            templateUtil.updateTemplate(t, new HashMap<>());
        }

        {
            // CC DirectDebitRejected Signature trial 4 reminder 2
            Template t = TemplateUtil.getTemplate("Accounting_dd_messaging_setup_client_notification_DirectDebitRejected_145");
            t.getChannelSetting(ChannelSpecificSettingType.Notification.toString())
                    .setText("We're sure your maid is very valuable to you and your family. Unfortunately, if you don't complete " +
                    "the Bank Payment Form today, our system will automatically cancel your service and notify your maid " +
                    "to come back to our accommodation. To continue your service. Please, would you take a moment to sign " +
                    "again right now on a piece of white paper and send us a photo of the paper by @link_send_dd_details_clicking_here@ .");
            templateUtil.updateTemplate(t, new HashMap<>());
        }

        {
            // CC DirectDebitRejected Signature trial 5 reminder 2
            Template t = TemplateUtil.getTemplate("Accounting_dd_messaging_setup_client_notification_DirectDebitRejected_146");
            t.getChannelSetting(ChannelSpecificSettingType.Notification.toString())
                    .setText("We're sure your maid is very valuable to you and your family. Unfortunately, if you don't complete " +
                    "the Bank Payment Form today, our system will automatically cancel your service and notify your maid to " +
                    "come back to our accommodation. To continue your service. Please, would you take a moment to sign again " +
                    "right now on a piece of white paper and send us a photo of the paper by @link_send_dd_details_clicking_here@ .");
            templateUtil.updateTemplate(t, new HashMap<>());
        }

        {
            // MV DirectDebitRejected Signature trial 1 reminder 2
            Template t = TemplateUtil.getTemplate("Accounting_dd_messaging_setup_client_notification_DirectDebitRejected_147");
            t.getChannelSetting(ChannelSpecificSettingType.Notification.toString())
                    .setText("We're sure your maid is very valuable to you and your family. Unfortunately, " +
                    "if you don't complete the Bank Payment Form today, our system will automatically cancel " +
                    "your service and your maid's visa. To continue your service, please @link_send_dd_details_click_here@ and sign.");
            templateUtil.updateTemplate(t, new HashMap<>());
        }

        {
            // MV DirectDebitRejected Signature trial 2 reminder 2
            Template t = TemplateUtil.getTemplate("Accounting_dd_messaging_setup_client_notification_DirectDebitRejected_149");
            t.getChannelSetting(ChannelSpecificSettingType.Notification.toString())
                    .setText("We're sure your maid is very valuable to you and your family. Unfortunately, if you don't complete " +
                    "the Bank Payment Form today, our system will automatically cancel your service and notify your maid " +
                    "to come back to our accommodation. To continue your service, please @link_send_dd_details_click_here@ and sign.");
            templateUtil.updateTemplate(t, new HashMap<>());
        }

        {
            // MV DirectDebitRejected Signature trial 3 reminder 2
            Template t = TemplateUtil.getTemplate("Accounting_dd_messaging_setup_client_notification_DirectDebitRejected_148");
            t.getChannelSetting(ChannelSpecificSettingType.Notification.toString())
                    .setText("We're sure your maid is very valuable to you and your family. Unfortunately, if you don't complete " +
                    "the Bank Payment Form today, our system will automatically cancel your service and your maid's visa. " +
                    "To continue your service. Please, would you take a moment to sign again right now on a piece of white paper " +
                    "and send us a photo of the paper by @link_send_dd_details_clicking_here@ .");
            templateUtil.updateTemplate(t, new HashMap<>());
        }

        {
            // MV DirectDebitRejected Signature trial 4 reminder 2
            Template t = TemplateUtil.getTemplate("Accounting_dd_messaging_setup_client_notification_DirectDebitRejected_150");
            t.getChannelSetting(ChannelSpecificSettingType.Notification.toString())
                    .setText("We're sure your maid is very valuable to you and your family. Unfortunately, if you don't complete " +
                    "the Bank Payment Form today, our system will automatically cancel your service and your maid's visa. " +
                    "To continue your service. Please, would you take a moment to sign again right now on a piece of white " +
                    "paper and send us a photo of the paper by @link_send_dd_details_clicking_here@ .");
            templateUtil.updateTemplate(t, new HashMap<>());
        }

        {
            // MV DirectDebitRejected Signature trial 5 reminder 2
            Template t = TemplateUtil.getTemplate("Accounting_dd_messaging_setup_client_notification_DirectDebitRejected_151");
            t.getChannelSetting(ChannelSpecificSettingType.Notification.toString())
                    .setText("We're sure your maid is very valuable to you and your family. Unfortunately, if you don't complete the " +
                    "Bank Payment Form today, our system will automatically cancel your service and your maid's visa. " +
                    "To continue your service. Please, would you take a moment to sign again right now on a piece of white " +
                    "paper and send us a photo of the paper by @link_send_dd_details_clicking_here@ .");
            templateUtil.updateTemplate(t, new HashMap<>());
        }

        {
            // CC DirectDebitRejected EID trial 1 reminder 2
            Template t = TemplateUtil.getTemplate("Accounting_dd_messaging_setup_client_notification_DirectDebitRejected_152");
            t.getChannelSetting(ChannelSpecificSettingType.Notification.toString())
                    .setText("We're sure your maid is very valuable to you and your family. Unfortunately, if you don't complete " +
                    "the Bank Payment Form today, our system will automatically cancel your service and notify your maid to " +
                    "come back to our accommodation. To continue your service, please @link_send_dd_details_click_here@ " +
                    "and send us the correct account holder Emirates ID.");
            templateUtil.updateTemplate(t, new HashMap<>());
        }

        {
            // CC DirectDebitRejected EID trial 2 reminder 2
            Template t = TemplateUtil.getTemplate("Accounting_dd_messaging_setup_client_notification_DirectDebitRejected_153");
            t.getChannelSetting(ChannelSpecificSettingType.Notification.toString())
                    .setText("We're sure your maid is very valuable to you and your family. Unfortunately, if you don't " +
                    "complete the Bank Payment Form today, our system will automatically cancel your service and " +
                    "notify your maid to come back to our accommodation. To continue your service, please @link_send_dd_details_click_here@ " +
                    "and send us the correct account holder Emirates ID.");
            templateUtil.updateTemplate(t, new HashMap<>());
        }

        {
            // CC DirectDebitRejected EID trial 3 reminder 2
            Template t = TemplateUtil.getTemplate("Accounting_dd_messaging_setup_client_notification_DirectDebitRejected_154");
            t.getChannelSetting(ChannelSpecificSettingType.Notification.toString())
                    .setText("We're sure your maid is very valuable to you and your family. Unfortunately, if you don't complete " +
                    "the Bank Payment Form today, our system will automatically cancel your service and notify your maid to " +
                    "come back to our accommodation. To continue your service, " +
                    "please @link_send_dd_details_click_here@ and send us the correct account holder Emirates ID.");
            templateUtil.updateTemplate(t, new HashMap<>());
        }

        {
            // MV DirectDebitRejected EID trial 1 reminder 2
            Template t = TemplateUtil.getTemplate("Accounting_dd_messaging_setup_client_notification_DirectDebitRejected_155");
            t.getChannelSetting(ChannelSpecificSettingType.Notification.toString())
                    .setText("We're sure your maid is very valuable to you and your family. Unfortunately, " +
                    "if you don't complete the Bank Payment Form today, our system will automatically cancel " +
                    "your service and your maid's visa. To continue your service, please @link_send_dd_details_click_here@ and send us " +
                    "the correct account holder Emirates ID.");
            templateUtil.updateTemplate(t, new HashMap<>());
        }

        {
            // MV DirectDebitRejected EID trial 2 reminder 2
            Template t = TemplateUtil.getTemplate("Accounting_dd_messaging_setup_client_notification_DirectDebitRejected_156");
            t.getChannelSetting(ChannelSpecificSettingType.Notification.toString())
                    .setText("We're sure your maid is very valuable to you and your family. Unfortunately, if you don't complete the Bank " +
                    "Payment Form today, our system will automatically cancel your service and your maid's visa. To continue your " +
                    "service, please @link_send_dd_details_click_here@ and send us the correct account holder Emirates ID.");
            templateUtil.updateTemplate(t, new HashMap<>());
        }

        {
            // MV DirectDebitRejected EID trial 3 reminder 2
            Template t = TemplateUtil.getTemplate("Accounting_dd_messaging_setup_client_notification_DirectDebitRejected_157");
            t.getChannelSetting(ChannelSpecificSettingType.Notification.toString())
                    .setText("We're sure your maid is very valuable to you and your family. Unfortunately, " +
                    "if you don't complete the Bank Payment Form today, our system will automatically cancel " +
                    "your service and your maid's visa. To continue your service, please @link_send_dd_details_click_here@ and send " +
                    "us the correct account holder Emirates ID.");
            templateUtil.updateTemplate(t, new HashMap<>());
        }

        {
            // CC DirectDebitRejected Account trial 1 reminder 2
            Template t = TemplateUtil.getTemplate("Accounting_dd_messaging_setup_client_notification_DirectDebitRejected_158");
            t.getChannelSetting(ChannelSpecificSettingType.Notification.toString())
                    .setText("We're sure your maid is very valuable to you and your family. Unfortunately, if you don't complete " +
                    "the Bank Payment Form today, our system will automatically cancel your service and notify your maid " +
                    "to come back to our accommodation. To continue your service, " +
                    "please @link_send_dd_details_click_here@ and send us the correct account holder name.");
            templateUtil.updateTemplate(t, new HashMap<>());
        }

        {
            // CC DirectDebitRejected Account trial 2 reminder 2
            Template t = TemplateUtil.getTemplate("Accounting_dd_messaging_setup_client_notification_DirectDebitRejected_160");
            t.getChannelSetting(ChannelSpecificSettingType.Notification.toString())
                    .setText("We're sure your maid is very valuable to you and your family. Unfortunately, if you don't " +
                    "complete the Bank Payment Form today, our system will automatically cancel your service and " +
                    "notify your maid to come back to our accommodation. To continue your service, " +
                    "please @link_send_dd_details_click_here@ and send us the correct account holder name.");
            templateUtil.updateTemplate(t, new HashMap<>());
        }

        {
            // CC DirectDebitRejected Account trial 3 reminder 2
            Template t = TemplateUtil.getTemplate("Accounting_dd_messaging_setup_client_notification_DirectDebitRejected_159");
            t.getChannelSetting(ChannelSpecificSettingType.Notification.toString())
                    .setText("We're sure your maid is very valuable to you and your family. Unfortunately, " +
                    "if you don't complete the Bank Payment Form today, our system will automatically cancel " +
                    "your service and notify your maid to come back to our accommodation. To continue your service, " +
                    "please @link_send_dd_details_click_here@ and send us the correct account holder name.");
            templateUtil.updateTemplate(t, new HashMap<>());
        }

        {
            // CC DirectDebitRejected Account trial 3 reminder 2
            Template t = TemplateUtil.getTemplate("Accounting_dd_messaging_setup_client_notification_DirectDebitRejected_159");
            t.getChannelSetting(ChannelSpecificSettingType.Notification.toString())
                    .setText("We're sure your maid is very valuable to you and your family. Unfortunately, " +
                    "if you don't complete the Bank Payment Form today, our system will automatically cancel " +
                    "your service and notify your maid to come back to our accommodation. To continue your service, " +
                    "please @link_send_dd_details_click_here@ and send us the correct account holder name.");
            templateUtil.updateTemplate(t, new HashMap<>());
        }

        {
            // MV DirectDebitRejected Account trial 1 reminder 2
            Template t = TemplateUtil.getTemplate("Accounting_dd_messaging_setup_client_notification_DirectDebitRejected_161");
            t.getChannelSetting(ChannelSpecificSettingType.Notification.toString())
                    .setText("We're sure your maid is very valuable to you and your family. Unfortunately, if you don't " +
                    "complete the Bank Payment Form today, our system will automatically cancel your service and your " +
                    "maid's visa. To continue your service, " +
                    "please @link_send_dd_details_click_here@ and send us the correct account holder name.");
            templateUtil.updateTemplate(t, new HashMap<>());
        }

        {
            // MV DirectDebitRejected Account trial 2 reminder 2
            Template t = TemplateUtil.getTemplate("Accounting_dd_messaging_setup_client_notification_DirectDebitRejected_162");
            t.getChannelSetting(ChannelSpecificSettingType.Notification.toString())
                    .setText("We're sure your maid is very valuable to you and your family. Unfortunately, if you don't " +
                    "complete the Bank Payment Form today, our system will automatically cancel your service and notify " +
                    "your maid to come back to our accommodation. To continue your service, " +
                    "please @link_send_dd_details_click_here@ and send us the correct account holder name.");
            templateUtil.updateTemplate(t, new HashMap<>());
        }

        {
            // MV DirectDebitRejected Account trial 3 reminder 2
            Template t = TemplateUtil.getTemplate("Accounting_dd_messaging_setup_client_notification_DirectDebitRejected_163");
            t.getChannelSetting(ChannelSpecificSettingType.Notification.toString())
                    .setText("We're sure your maid is very valuable to you and your family. Unfortunately, if you don't complete " +
                    "the Bank Payment Form today, our system will automatically cancel your service and notify your maid to " +
                    "come back to our accommodation. To continue your service, please @link_send_dd_details_click_here@ " +
                    "and send us the correct account holder name.");
            templateUtil.updateTemplate(t, new HashMap<>());
        }

        {
            // CC DirectDebitRejected Invalid Account trial 1 reminder 2
            Template t = TemplateUtil.getTemplate("Accounting_dd_messaging_setup_client_notification_DirectDebitRejected_164");
            t.getChannelSetting(ChannelSpecificSettingType.Notification.toString())
                    .setText("We're sure your maid is very valuable to you and your family. Unfortunately, if you don't " +
                    "complete the Bank Payment Form, our system will automatically cancel your service. To continue " +
                    "your service, please @link_send_dd_details_click_here@ and send us a photo of a different bank account that's set in AED.");
            templateUtil.updateTemplate(t, new HashMap<>());
        }

        {
            // CC DirectDebitRejected Invalid Account trial 2 reminder 2
            Template t = TemplateUtil.getTemplate("Accounting_dd_messaging_setup_client_notification_DirectDebitRejected_165");
            t.getChannelSetting(ChannelSpecificSettingType.Notification.toString())
                    .setText("We're sure your maid is very valuable to you and your family. Unfortunately, if you don't complete " +
                    "the Bank Payment Form, our system will automatically cancel your service. To continue your service, " +
                    "please @link_send_dd_details_click_here@ and send us a photo of a different bank account that's set in AED.");
            templateUtil.updateTemplate(t, new HashMap<>());
        }

        {
            // CC DirectDebitRejected Invalid Account trial 3 reminder 2
            Template t = TemplateUtil.getTemplate("Accounting_dd_messaging_setup_client_notification_DirectDebitRejected_166");
            t.getChannelSetting(ChannelSpecificSettingType.Notification.toString())
                    .setText("We're sure your maid is very valuable to you and your family. Unfortunately, if you don't complete " +
                    "the Bank Payment Form, our system will automatically cancel your service and notify your maid to " +
                    "come back to our accommodation. To continue your service, please @link_send_dd_details_click_here@ and send us a photo of " +
                    "a different bank account that's set in AED.");
            templateUtil.updateTemplate(t, new HashMap<>());
        }

        {
            // MV DirectDebitRejected Invalid Account trial 1 reminder 2
            Template t = TemplateUtil.getTemplate("Accounting_dd_messaging_setup_client_notification_DirectDebitRejected_167");
            t.getChannelSetting(ChannelSpecificSettingType.Notification.toString())
                    .setText("We're sure your maid is very valuable to you and your family. Unfortunately, " +
                    "if you don't complete the Bank Payment Form, our system will automatically cancel your " +
                    "service and your maid's visa. To continue your service, please @link_send_dd_details_click_here@ and send us a " +
                    "photo of a different bank account that's set in AED.");
            templateUtil.updateTemplate(t, new HashMap<>());
        }

        {
            // MV DirectDebitRejected Invalid Account trial 2 reminder 2
            Template t = TemplateUtil.getTemplate("Accounting_dd_messaging_setup_client_notification_DirectDebitRejected_168");
            t.getChannelSetting(ChannelSpecificSettingType.Notification.toString())
                    .setText("We're sure your maid is very valuable to you and your family. Unfortunately, if you don't complete " +
                    "the Bank Payment Form, our system will automatically cancel your service and your maid's visa. " +
                    "To continue your service, please @link_send_dd_details_click_here@ and send us a photo of a different bank account that's set in AED.");
            templateUtil.updateTemplate(t, new HashMap<>());
        }

        {
            // MV DirectDebitRejected Invalid Account trial 3 reminder 2
            Template t = TemplateUtil.getTemplate("Accounting_dd_messaging_setup_client_notification_DirectDebitRejected_169");
            t.getChannelSetting(ChannelSpecificSettingType.Notification.toString())
                    .setText("We're sure your maid is very valuable to you and your family. Unfortunately, if you don't complete " +
                    "the Bank Payment Form, our system will automatically cancel your service and your maid's visa. " +
                    "To continue your service, please @link_send_dd_details_click_here@ and send us a photo of a different bank account that's set in AED.");
            templateUtil.updateTemplate(t, new HashMap<>());
        }

        return new ResponseEntity<>("Done", HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('DDMessaging','updateNotificationsACC5534')")
    @PostMapping("/updateNotificationsACC5534")
    public ResponseEntity<?> updateNotificationsACC5534()  {

        TemplateUtil templateUtil = Setup.getApplicationContext().getBean(TemplateUtil.class);

        PicklistItem priority_1 = Setup.getItem("priority_1", "template_priority");
        PicklistItem priority_2 = Setup.getItem("priority_2", "template_priority");
        PicklistItem priority_3 = Setup.getItem("priority_3", "template_priority");
        PicklistItem priority_4 = Setup.getItem("priority_4", "template_priority");

        SelectQuery<DDMessaging> query = new SelectQuery<>(DDMessaging.class);
        query.execute().forEach(d -> {
            Template t = d.getClientTemplate();
            if (t == null) return;
            PicklistItem priority = priority_2;

            if (d.getClientMessagePriority() != null) {
                switch (d.getClientMessagePriority()) {
                    case SEND_SMS_WITH_NOTIFICATION:
                        priority = priority_1;
                        break;
                    case SEND_SMS_NEXT_DAY:
                        priority = priority_3;
                        break;
                    case DO_NOT_SEND_SMS:
                        priority = priority_4;
                        break;
                }
            }

            t.setPriority(priority);
            templateUtil.updateTemplate(t, new HashMap<>());
        });

        List<String> templateCodes = Arrays.asList(
                CcNotificationTemplateCode.CC_CLIENT_REFUND_BANK_TRANSFER_DETAILS_NOTIFICATION.toString(),
                CcNotificationTemplateCode.CC_PAYTAB_THANKS_MESSAGE_NOTIFICATION.toString(),
                MvNotificationTemplateCode.MV_PAYTABS_THANKS_MESSAGE_NOTIFICATION.toString(),
                DirectDebitGenerationPlanTemplate.INSURANCE.toString(),
                MvNotificationTemplateCode.MV_DIRECT_DEBIT_GENERATION_PLAN_INSURANCE_NOTIFICATION.toString(),
                DirectDebitGenerationPlanTemplate.SAME_DAY_RECRUITMENT_FEE.toString(),
                MvNotificationTemplateCode.MV_DIRECT_DEBIT_GENERATION_PLAN_SAME_DAY_RECRUITMENT_FEE_NOTIFICATION.toString(),
                DirectDebitGenerationPlanTemplate.OTHER_DD_TYPE.toString(),
                MvNotificationTemplateCode.MV_DIRECT_DEBIT_GENERATION_PLAN_OTHER_DD_TYPE_NOTIFICATION.toString(),
                MvNotificationTemplateCode.MV_PAYMENT_EXPIRY_4_1_1_NOTIFICATION.toString(),
                MvNotificationTemplateCode.MV_PAYMENT_EXPIRY_4_1_2_NOTIFICATION.toString(),
                CcNotificationTemplateCode.CC_ACCOUNTING_NOT_OWED_MONEY_FROM_CLIENT_8_1_2_NOTIFICATION.toString(),
                MvNotificationTemplateCode.MV_ACCOUNTING_NOT_OWED_MONEY_FROM_CLIENT_8_1_2_NOTIFICATION.toString(),
                CcNotificationTemplateCode.CC_ACCOUNTING_WRONGLY_CHARGED_MONEY_ON_CLIENT_8_1_3_NOTIFICATION.toString(),
                MvNotificationTemplateCode.MV_ACCOUNTING_WRONGLY_CHARGED_MONEY_ON_CLIENT_8_1_3_NOTIFICATION.toString(),
                CcNotificationTemplateCode.CC_ACCOUNTING_OWE_MONEY_TO_CLIENT_8_1_1_NOTIFICATION.toString(),
                CcNotificationTemplateCode.CC_PAYMENT_UNDER_PROCESSING_8_1_6_NOTIFICATION.toString(),
                CcNotificationTemplateCode.CC_PAYMENT_RECEIVED_8_1_6_NOTIFICATION.toString(),
                CcNotificationTemplateCode.CC_PAYMENT_NOT_RECEIVED_OR_BOUNCED_8_1_6_NOTIFICATION.toString(),
                CcNotificationTemplateCode.CC_PAYMENT_UNDER_PROCESSING_8_1_5_NOTIFICATION.toString(),
                CcNotificationTemplateCode.CC_PAYMENT_RECEIVED_8_1_5_NOTIFICATION.toString(),
                CcNotificationTemplateCode.CC_PAYMENT_NOT_RECEIVED_OR_BOUNCED_8_1_5_NOTIFICATION.toString());

        SelectQuery<Template> tQuery = new SelectQuery<>(Template.class);
        tQuery.filterBy("code", "in", templateCodes);

        tQuery.execute().forEach(t -> {
            t.setPriority(priority_3);
            templateUtil.updateTemplate(t, new HashMap<>());
        });

        Template t = TemplateUtil.getTemplate(CcNotificationTemplateCode.CC_DD_PENDING_INFO_NOTIFICATION.toString());
        t.setPriority(priority_2);
        templateUtil.updateTemplate(t, new HashMap<>());

        t = TemplateUtil.getTemplate(MvNotificationTemplateCode.MV_DD_PENDING_INFO_NOTIFICATION.toString());
        t.setPriority(priority_2);
        templateUtil.updateTemplate(t, new HashMap<>());

        return new ResponseEntity<>("Done", HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('DDMessaging','addAllowedParameters')")
    @GetMapping(value = "/addAllowedParameters")
    @Transactional
    public ResponseEntity<?> addAllowedParameters(){

        Setup.getApplicationContext()
                .getBean(MessagingService.class)
                .addGenderAndWorkerTypeForTemplates();

        return ResponseEntity.ok("Done");
    }

    @PreAuthorize("hasPermission('DDMessaging','updateNotifications6255')")
    @PostMapping("/updateNotifications6255")
    public ResponseEntity<?> updateNotificationsACC6255(
            MultipartFile file) throws IOException {

        ddMessagingService.updateNotificationsACC6255ReadSheetDataIPAM(file);
        ddMessagingService.updateNotificationsACC6255ReadSheetDataOCCR(file);
        ddMessagingService.updateNotificationsACC6255ReadSheetDataPaymentForApprovalRequest(file);

        return new ResponseEntity<>("Done", HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('DDMessaging','createAllowedParametersForOldDdMessaging')")
    @GetMapping(value = "/createAllowedParametersForOldDdMessaging")
    @Transactional
    public ResponseEntity<?> createAllowedParametersForOldDdMessaging() {

        // rejection flow
        ddMessagingService.createAllowedParametersForOldDdMessaging(DDMessagingType.DirectDebitRejected, null);

        // IPAM FLOW
        ddMessagingService.createAllowedParametersForOldDdMessaging(DDMessagingType.ClientPaidCashAndNoSignatureProvided, DDMessagingSubType.NO_SIGNATURE);
        ddMessagingService.createAllowedParametersForOldDdMessaging(DDMessagingType.ClientPaidCashAndNoSignatureProvided, DDMessagingSubType.NO_SIGNATURE_WITH_ONE_PAYMENT_ONLY);

        // incomplete DD flow
        ddMessagingService.createAllowedParametersForOldDdMessaging(DDMessagingType.IncompleteDDRejectedByDataEntry, null);

        // bounced payment flow
        ddMessagingService.createAllowedParametersForOldDdMessaging(DDMessagingType.BouncedPayment, null);

        // collect signatures flow
        ddMessagingService.createAllowedParametersForOldDdMessaging(DDMessagingType.IncompleteDDClientHasNoApprovedSignature, null);

        // expiry payment flow
        ddMessagingService.createAllowedParametersForOldDdMessaging(DDMessagingType.ExpiryPayment, null);

        // online reminder flow
        ddMessagingService.createAllowedParametersForOldDdMessaging(DDMessagingType.OnlineCreditCardPaymentReminders, DDMessagingSubType.PENDING_PAYMENT);

        // clients paying via credit card flow
        ddMessagingService.createAllowedParametersForOldDdMessaging(DDMessagingType.ClientsPayingViaCreditCard, DDMessagingSubType.DD_Rejection);
        ddMessagingService.createAllowedParametersForOldDdMessaging(DDMessagingType.ClientsPayingViaCreditCard, DDMessagingSubType.INITIAL_FLOW_FOR_DDB);
        ddMessagingService.createAllowedParametersForOldDdMessaging(DDMessagingType.ClientsPayingViaCreditCard, DDMessagingSubType.INITIAL_FLOW_FOR_DDA);
        ddMessagingService.createAllowedParametersForOldDdMessaging(DDMessagingType.ClientsPayingViaCreditCard, DDMessagingSubType.DD_SIGNING_OFFER);
        ddMessagingService.createAllowedParametersForOldDdMessaging(DDMessagingType.ClientsPayingViaCreditCard, DDMessagingSubType.MONTHLY_REMINDER);

        // OMA FLOW
//        ddMessagingService.createAllowedParametersForOldDdMessaging(DDMessagingType.OneMonthAgreement, DDMessagingSubType.PAYMENT_REMINDER);
//        ddMessagingService.createAllowedParametersForOldDdMessaging(DDMessagingType.OneMonthAgreement, DDMessagingSubType.SIGNING_OFFER_AND_CC_PAYMENT_ALLOWED);
//        ddMessagingService.createAllowedParametersForOldDdMessaging(DDMessagingType.OneMonthAgreement, DDMessagingSubType.SIGNING_OFFER_AND_CC_PAYMENT_IS_NOT_ALLOWED);

        return ResponseEntity.ok("Done");
    }

    @PreAuthorize("hasPermission('DDMessaging','updateNotificationsAcc6344')")
    @PostMapping("/updateNotificationsAcc6344")
    public ResponseEntity<?> updateNotificationsAcc6344(MultipartFile file) throws IOException {

        ddMessagingService.updateNotificationsAcc6344ReadSheetData(file);

        return ResponseEntity.ok("Done");
    }

    @PreAuthorize("hasPermission('DDMessaging','updateNotificationsACC6344')")
    @PostMapping("/updateNotifications6344")
    public ResponseEntity<?> updateNotificationsACC6344(MultipartFile file) throws IOException {

        ddMessagingService.updateNotificationsAcc6344ReadSheetData(file);

        return ResponseEntity.ok("Done");
    }

    @PreAuthorize("hasPermission('DDMessaging','addCreditCardOfferParameterAcc6624_DdBankMessages')")
    @GetMapping("/addCreditCardOfferParameterAcc6624_DdBankMessages")
    public ResponseEntity<?> addCreditCardOfferParameterAcc6624_DdBankMessages() {

        ddMessagingService.addCreditCardOfferParameterAcc6624_DdBankMessages();

        return ResponseEntity.ok("Done");
    }

    @PreAuthorize("hasPermission('DDMessaging','updateNotificationsAcc6804')")
    @PostMapping("/updateNotificationsAcc6804")
    public ResponseEntity<?> updateNotificationsAcc6804(MultipartFile file) throws IOException {

        ddMessagingService.updateNotificationsAcc6804ReadSheetData(file);

        return ResponseEntity.ok("Done");
    }

    @PreAuthorize("hasPermission('DDMessaging','updateNotificationsAcc6668')")
    @PostMapping("/updateNotificationsAcc6668")
    public ResponseEntity<?> updateNotificationsAcc6668(MultipartFile file) throws IOException {

        ddMessagingService.updateNotificationsAcc6668ReadSheetData(file, "Incomplete DD Flow");
        ddMessagingService.updateNotificationsAcc6668ReadSheetData(file, "OTHER TEMPLATES");

        return ResponseEntity.ok("Done");
    }
    @PreAuthorize("hasPermission('DDMessaging','updateNotificationsAcc6795')")
    @PostMapping("/updateNotificationsAcc6795")
    public ResponseEntity<?> updateNotificationsAcc6795(MultipartFile file) throws IOException {

        ddMessagingService.updateNotificationsAcc6795ReadSheet(file);

        return ResponseEntity.ok("Done");
    }

    @PreAuthorize("hasPermission('DDMessaging','acc7505DataMigration')")
    @GetMapping("/acc7505DataMigration")
    public ResponseEntity<?> acc7505DataMigration() {
        DisablePushNotificationRepository disablePushNotificationRepository = Setup.getRepository(DisablePushNotificationRepository.class);

        Long lastId = -1L;
        List<PushNotification> l = disablePushNotificationRepository.findForAcc7505DataCorrection_bouncedPayment(lastId);
        while (!l.isEmpty()) {
            l.forEach(p -> {
                logger.info("p id: " + p.getId());
                p.setLocation(NotificationLocation.INBOX);
                p.setDisabled(true);
            });
            disablePushNotificationRepository.saveAll(l);

            lastId = l.get(l.size() - 1).getId();
            l = disablePushNotificationRepository.findForAcc7505DataCorrection_bouncedPayment(lastId);
        }

        lastId = -1L;
        l = disablePushNotificationRepository.findForAcc7505DataCorrection_oldSentToSpouse(lastId);
        while (!l.isEmpty()) {
            l.forEach(p -> {
                logger.info("p id: " + p.getId());
                p.setLocation(NotificationLocation.INBOX);
                p.setDisabled(true);
            });
            disablePushNotificationRepository.saveAll(l);

            lastId = l.get(l.size() - 1).getId();
            l = disablePushNotificationRepository.findForAcc7505DataCorrection_oldSentToSpouse(lastId);
        }

        return ResponseEntity.ok("Done");
    }
    @PreAuthorize("hasPermission('DDMessaging','addCreditCardOfferParameterAcc6624')")
    @GetMapping("/addCreditCardOfferParameterAcc6624")
    public ResponseEntity<?> addCreditCardOfferParameterAcc6624() {

        ddMessagingService.addCreditCardOfferParameterAcc6624();
        return ResponseEntity.ok("Done");
    }


    @PreAuthorize("hasPermission('DDMessaging','updateNotificationsAcc6563')")
    @PostMapping("/updateNotificationsAcc6563")
    @Transactional
    public ResponseEntity<?> updateNotificationsAcc6563(MultipartFile file) throws IOException {

        ddMessagingService.updateNotificationsAcc6563(file);

        return ResponseEntity.ok("Done");
    }

    @PreAuthorize("hasPermission('DDMessaging','updateClientViaCCMessagesAcc6840')")
    @PostMapping("/updateClientViaCCMessagesAcc6840")
    @Transactional
    public ResponseEntity<?> updateClientViaCCMessagesAcc6840(MultipartFile file) throws IOException {

        SelectQuery<DDMessaging> q = new SelectQuery<>(DDMessaging.class);
        q.filterBy("event", "=", DDMessagingType.ClientsPayingViaCreditCard);
        q.filterBy("subType", "in", Arrays.asList(DDMessagingSubType.INSUFFICIENT_FUNDS,
                                                                DDMessagingSubType.EXCEEDING_DAILY_LIMITS,
                                                                DDMessagingSubType.EXPIRED_CARD,
                                                                DDMessagingSubType.TOKEN_DELETED,
                                                                DDMessagingSubType.ACCOUNT_ISSUE,
                                                                DDMessagingSubType.OTHER_ISSUES) );
        List<DDMessaging> l = q.execute();
        boolean isFirstTime = l.isEmpty() ? true : false ;

        ddMessagingService.updateClientViaCCMessagesAcc6840(file, "Insufficient Funds", l, isFirstTime);
        ddMessagingService.updateClientViaCCMessagesAcc6840(file, "Exceeds Card Limit", l, isFirstTime);
        ddMessagingService.updateClientViaCCMessagesAcc6840(file, "Expiring Card", l, isFirstTime);
        ddMessagingService.updateClientViaCCMessagesAcc6840(file, "Card or Account Issue", l, isFirstTime);
        ddMessagingService.updateClientViaCCMessagesAcc6840(file, "OTHERs", l, isFirstTime);
        ddMessagingService.updateClientViaCCMessagesAcc6840(file, "Token Deleted", l, isFirstTime);
        return ResponseEntity.ok("Done");
    }

    @PreAuthorize("hasPermission('DDMessaging','sendMessageToClientAcc6385')")
    @PostMapping("/sendMessageToClientAcc6385")
    public ResponseEntity<?> sendMessageToClientAcc6385(@RequestBody Map<String, Object> body) {

        // find contract by contractId
        Contract contract = Setup.getRepository(ContractRepository.class).findOne(Long.parseLong(body.get("contractId").toString()));
        if(contract == null) return notFoundResponse();

        // find ddMessagingContract by ddMessagingType
        DDMessagingContract ddMessagingContract;
        switch ((String) body.get("relatedEntityType")) {
            case "DDMessaging":
                ddMessagingContract = ddMessagingRepository.findOne(Long.parseLong(body.get("relatedEntityId").toString()));
                break;
            case "DDBankMessaging":
                ddMessagingContract = Setup.getRepository(DDBankMessagingRepository.class)
                        .findOne(Long.parseLong(body.get("relatedEntityId").toString()));
                break;
            default:
                return notFoundResponse();
        }

        // fill parameters
        Map<String, String> parameters = (Map<String, String>) body.get("extraInfo");

        Setup.getApplicationContext()
                .getBean(FlowProcessorMessagingService.class)
                .fillParameters(
                        ddMessagingContract,
                        parameters.containsKey("flowProcessorEntityId") && parameters.get("flowProcessorEntityId") != null ?
                            Setup.getRepository(FlowProcessorEntityRepository.class)
                                    .findOne(Long.parseLong(parameters.get("flowProcessorEntityId"))) :
                            null,
                        contract.getActiveContractPaymentTerm(), parameters, true);

        // send message by channelType
        Setup.getApplicationContext()
                .getBean(MessagingService.class)
                .sendClientMessageByType(contract,
                        ddMessagingContract.getClientTemplate(),
                        parameters,
                        new HashMap(),
                        (String) body.get("mobileNumber"),
                        (String) body.get("mobileNumber"),
                        contract.getId(),
                        contract.getEntityType(),
                        ChannelSpecificSettingType.valueOf((String) body.get("channelType")));
        return okResponse();
    }

    @PreAuthorize("hasPermission('DDMessaging','createTerminationMessagesAcc8949')")
    @PostMapping("/createTerminationMessagesAcc8949")
    public ResponseEntity<?> createTerminationMessagesAcc8949(@RequestBody MultipartFile file) throws IOException {

        ddMessagingService.createTerminationMessagesAcc8949(file);
        return ResponseEntity.ok("Done");
    }

    @PostMapping("/createExtensionFlowMessagesAcc8954")
    public ResponseEntity<?> createExtensionFlowMessagesAcc8954(@RequestBody MultipartFile file) throws IOException {

        ddMessagingService.createExtensionFlowMessagesAcc8954(file);
        return ResponseEntity.ok("Done");
    }

    @PreAuthorize("hasPermission('DDMessaging','updateReminderFlowMessagesAcc6624')")
    @PostMapping("/updateReminderFlowMessagesAcc9286")
    @Transactional
    public ResponseEntity<?> updateReminderFlowMessagesAcc9286(MultipartFile file) throws IOException {

        XSSFWorkbook workbook = new XSSFWorkbook(file.getInputStream());

        ddMessagingService.updateReminderFlowMessagesAcc9286(workbook, "Multiple Required Flow", DDMessagingPaymentStructure.MULTIPLE_PAYMENTS);
        ddMessagingService.updateReminderFlowMessagesAcc9286(workbook, "One Payment Required Flow", DDMessagingPaymentStructure.ONE_PAYMENT);
        ddMessagingService.updateReminderFlowMessagesAcc9286(workbook, "Multiple Non Required Flow", DDMessagingPaymentStructure.MULTIPLE_PAYMENTS);
        ddMessagingService.updateReminderFlowMessagesAcc9286(workbook, "One Payment Non Required Flow", DDMessagingPaymentStructure.ONE_PAYMENT);

        ddMessagingService.updateReminderFlowMessagesAcc9286(workbook, "Multiple Initial Flow", DDMessagingPaymentStructure.MULTIPLE_PAYMENTS);
        ddMessagingService.updateReminderFlowMessagesAcc9286(workbook, "One Payment Initial Flow", DDMessagingPaymentStructure.ONE_PAYMENT);
        return ResponseEntity.ok("Done");
    }
}