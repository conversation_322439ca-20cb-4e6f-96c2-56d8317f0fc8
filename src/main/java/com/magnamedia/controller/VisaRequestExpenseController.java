package com.magnamedia.controller;

import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.Searchable;
import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.helper.CurrentRequest;
import com.magnamedia.core.helper.SelectFilter;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.*;
import com.magnamedia.entity.projection.VisaRequestExpensesCsvProjection;
import com.magnamedia.extra.*;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.helper.PicklistHelper;
import com.magnamedia.module.type.MaidContractType;
import com.magnamedia.module.type.PaymentType;
import com.magnamedia.module.type.VatType;
import com.magnamedia.repository.NewVisaRequestExpenseRepository;
import com.magnamedia.repository.*;
import com.magnamedia.service.VisaExpenseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;

import java.util.logging.Level;
import java.util.stream.Collectors;

import java.util.ArrayList;

/**
 * <AUTHOR> Kanaan <<EMAIL>>
 *         Created on Jan 14, 2018
 */
@RequestMapping("/visarequestexpense")
@RestController
public class VisaRequestExpenseController extends BaseRepositoryController<NewRequestExpense> {

    @Autowired
    private NewVisaRequestExpenseRepository newVisaRequestExpenseRepository;
    @Autowired
    private RenewVisaRequestExpenseRepository renewVisaRequestExpenseRepository;
    @Autowired
    private CancelVisaRequestExpenseRepository cancelVisaRequestExpenseRepository;
    @Autowired
    private RepeatEIDExpenseRepository repeatEIDExpenseRepository;
    @Autowired
    private ContractModificationExpenseRepository contractModificationExpenseRepository;
    @Autowired
    private ModifyVisaRequestExpenseRepository modifyVisaRequestExpenseRepository;

    @Autowired
    private TransactionsController transactionsController;
    @Autowired
    private VisaExpenseService visaExpenseService;

    @Override
    public BaseRepository<NewRequestExpense> getRepository() {
        return this.newVisaRequestExpenseRepository;
    }

    @PreAuthorize("hasPermission('visarequestexpense','search')")
    @RequestMapping(value = "/page/search", method = RequestMethod.GET)
    public ResponseEntity<?> searchVisaRequestExpense(
            Pageable pageable,
            @RequestParam(name = "searchid", required = false) Long searchid,
            @RequestParam(name = "searchname", required = false) String searchname,
            @RequestParam(name = "searchstatus", required = false) String searchstatus) {

        if (searchid != null) {
            return new ResponseEntity<>(
                    visaExpenseService.searchById(new SelectFilter("id", "=", searchid)),
                    HttpStatus.OK);
        }

        // ACC-352 ACC-558
        SelectFilter filters = new SelectFilter("status", "=", searchstatus);
        if (searchname != null && !searchname.isEmpty()) {
            filters.and("employeeName", "like", "%" + searchname + "%");
        }

        return new ResponseEntity<>(
                visaExpenseService.getSearchData(filters, pageable),
                HttpStatus.OK);
    }

    // ACC-558
    @PreAuthorize("hasPermission('visarequestexpense','search')")
    @GetMapping(value = "/csv/search")
    public ResponseEntity<?> searchVisaRequestExpenseCSV(
            HttpServletResponse response,
            @RequestParam(name = "searchid", required = false) Long searchid,
            @RequestParam(name = "searchname", required = false) String searchname,
            @RequestParam(name = "searchstatus", required = false) String searchstatus) {

        SelectFilter filters;
        if (searchid != null) {
            filters = new SelectFilter("id", "=", searchid);

        } else {
            filters = new SelectFilter("status", "=", searchstatus);
            if (searchname != null && !searchname.isEmpty()) {
                filters.and("employeeName", "like", "%" + searchname + "%");
            }
        }

        generateCsvFile(visaExpenseService.getCsvData(filters), response);
        return new ResponseEntity<>("", HttpStatus.OK);

    }

    @PreAuthorize("hasPermission('visarequestexpense','dismiss')")
    @GetMapping(value = "/dismiss")
    public ResponseEntity<?> dismissVisaRequestExpense(
            @RequestParam(name = "id", required = false) Long visaRequestExpenseID,
            @RequestParam(name = "type", required = false) String visaRequestExpenseType) {

        if (visaRequestExpenseType == null)
            return new ResponseEntity<>("Please specify the visa request expense type.", HttpStatus.BAD_REQUEST);

        String n = visaExpenseService.dismissVisaRequest(visaRequestExpenseType, visaRequestExpenseID);
        if (n == null) return new ResponseEntity<>("Visa request expense not found.", HttpStatus.BAD_REQUEST);

        return new ResponseEntity<>("Visa @expense_name@ dismissed successfully."
                            .replace("@expense_name@", n), HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('visarequestexpense','addtransaction')")
    @RequestMapping(value = "/addtransaction", method = RequestMethod.POST)
    @Transactional
    public ResponseEntity<?> addVisaRequestExpenseTransaction(
            @RequestParam(name = "id", required = false) Long visaRequestExpenseID,
            @RequestParam(name = "type", required = false) String visaRequestExpenseType,
            @RequestBody Transaction transaction) {

        if (visaRequestExpenseType == null)
            return new ResponseEntity<>("Please specify the visa request expense type.", HttpStatus.BAD_REQUEST);

        VisaExpense expense = visaExpenseService.getVisaExpenseByType(visaRequestExpenseType, visaRequestExpenseID);
        if (expense == null)
            return new ResponseEntity<>("Visa request expense not found.", HttpStatus.BAD_REQUEST);

        //  ACC-1314
        checkValidity(expense);

        switch (visaRequestExpenseType.toLowerCase()) {
            case "newrequestexpense":
                transaction.setNewRequestExpense((NewRequestExpense) expense);
                break;
            case "renewrequestexpense":
                transaction.setRenewRequestExpense((RenewRequestExpense) expense);
                break;
            case "cancelrequestexpense":
                transaction.setCancelRequestExpense((CancelRequestExpense) expense);
                break;
            case "repeateidrequestexpense":
                transaction.setRepeatEIDRequestExpense((RepeatEIDRequestExpense) expense);
                break;
            case "contractmodificationexpense":
                transaction.setContractModificationExpense((ContractModificationExpense) expense);
                break;
            case "modifyvisarequestexpense":
                transaction.setModifyVisaRequestExpense((ModifyVisaRequestExpense) expense);
                break;
            case "unpaidleaveexpense":
                transaction.setUnpaidLeaveExpense((UnpaidLeaveExpense) expense);
                break;
            case "modifypersoninformationexpense":
                transaction.setModifyPersonInformationExpense((ModifyPersonInformationExpense) expense);
                break;
        }

        transaction.setCreationTriggeredAutomatically(true);

        VisaExpensePaymentTypeDetails v = Setup.getRepository(VisaExpensePaymentTypeDetailsRepository.class)
                .findFirstByPaymentTypeAndExpensePurpose(expense.getPaymentType(), expense.getPurpose());
        if (v != null) {
            transaction.setVatAmount(VisaExpenseService.calculateVatViaVisaExpensePaymentTypeDetails(v));
            if (transaction.getVatAmount() > 0) {
                transaction.setVatType(VatType.IN);
            }
        } else {
            // ACC-6912
            transaction.setLicense(PicklistHelper.getItemNoException(
                    AccountingModule.PICKLIST_TRANSACTION_LICENSE,
                    AccountingModule.PICKLIST_TRANSACTION_LICENSE_NO_VAT_ITEM));
        }

        expense.getAttachments()
                .stream()
                .filter(a -> a.getTag().toLowerCase().startsWith("receipt -"))
                .forEach(transaction::addAttachment);

        ResponseEntity<?> response =
                transactionsController.newCreateEntity(transaction);

        if (response.getStatusCode().equals(HttpStatus.OK)) {
            expense.setTransaction(transaction);
            expense.setStatus(ExpenseStatus.Added);

            visaExpenseService.saveVisaExpenseByType(visaRequestExpenseType, expense);

            return new ResponseEntity<>("Visa  @expense_name@ added successfully."
                    .replace("@expense_name@", expense.getName()), HttpStatus.OK);
        }

        return response;
    }

    // ACC-462
    @PreAuthorize("hasPermission('visarequestexpense','dismiss')")
    @PostMapping(value = "/list/dismiss")
    @Transactional
    public ResponseEntity<?> dismissVisaRequestExpenseList(
            @RequestBody List<AddDissmissVisaExpense> list) {

        for (AddDissmissVisaExpense e : list) {
            this.dismissVisaRequestExpense(e.getVisaRequestExpenseID(), e.getVisaRequestExpenseType());
        }

        return new ResponseEntity<>("Done.", HttpStatus.OK);
    }

    // ACC-462
    @PreAuthorize("hasPermission('visarequestexpense','addtransaction')")
    @PostMapping(value = "/list/addtransaction")
    public ResponseEntity<?> addVisaRequestExpenseTransactionList(
            @RequestBody List<AddDissmissVisaExpense> list) {

        for (AddDissmissVisaExpense e : list) {
            this.addVisaRequestExpenseTransaction(
                    e.getVisaRequestExpenseID(),
                    e.getVisaRequestExpenseType(),
                    e.getTransaction());
        }

        return new ResponseEntity<>("Done.", HttpStatus.OK);
    }

    //  ACC-1314
    private void checkValidity(VisaExpense expense) {
        if (expense.getStatus() != null && expense.getStatus().equals(ExpenseStatus.Added) && expense.getTransaction() != null)
            throw new RuntimeException("Expense @expense_name@ was already added in transaction @transaction_id@"
                    .replace("@expense_name@", expense.getName())
                    .replace("@transaction_id@", expense.getTransaction().getId().toString()));
    }

    // ACC-558
    @PreAuthorize("hasPermission('visarequestexpense','getallexpensestatuses')")
    @RequestMapping(value = "/getallexpensestatuses")
    public ResponseEntity<?> getAllExpenseStatuses(
            @RequestParam(name = "search", required = false) String search) {
        List<SearchableEnumProjection> result =
                Arrays.asList(ExpenseStatus.class.getEnumConstants())
                        .stream()
                        .map(x -> new SearchableEnumProjection(x.toString()))
                        .filter(x -> search == null
                                || search.isEmpty()
                                || x.getLabel().contains(search))
                        .collect(Collectors.toList());
        return new ResponseEntity<>(
                result,
                HttpStatus.OK);
    }

    // ACC-558
    @PreAuthorize("hasPermission('visarequestexpense','getallpaymenttypes')")
    @RequestMapping(value = "/getallpaymenttypes")
    public ResponseEntity<?> getAllPaymentTypes(
            @RequestParam(name = "search", required = false) String search) {
        List<SearchableEnumProjection> result =
                Arrays.asList(PaymentType.class.getEnumConstants())
                        .stream()
                        .map(x -> new SearchableEnumProjection(x.toString()))
                        .filter(x -> search == null
                                || search.isEmpty()
                                || x.getLabel().contains(search))
                        .collect(Collectors.toList());
        return new ResponseEntity<>(
                result,
                HttpStatus.OK);
    }

    // ACC-558
    @PreAuthorize("hasPermission('visarequestexpense','getallemployeetypes')")
    @RequestMapping(value = "/getallemployeetypes")
    public ResponseEntity<?> getAllEmployeeTypes(
            @RequestParam(name = "search", required = false) String search) {
        List<SearchableEnumProjection> result =
                Arrays.asList("Housemaid", "Officestaff")
                        .stream()
                        .map(x -> new SearchableEnumProjection(x))
                        .filter(x -> search == null
                                || search.isEmpty()
                                || x.getLabel().contains(search))
                        .collect(Collectors.toList());
        return new ResponseEntity<>(
                result,
                HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('visarequestexpense','getallmaidcontracttypes')")
    @RequestMapping(value = "/getallmaidcontracttypes")
    public ResponseEntity<?> getAllMaidContractTypes(
            @RequestParam(name = "search", required = false) String search) {
        List<SearchableEnumProjection> result =
                Arrays.asList(MaidContractType.class.getEnumConstants())
                        .stream()
                        .map(x -> new SearchableEnumProjection(x))
                        .filter(x -> search == null
                                || search.isEmpty()
                                || x.getLabel().contains(search))
                        .collect(Collectors.toList());
        return new ResponseEntity<>(
                result,
                HttpStatus.OK);
    }

    // ACC-558
    @PreAuthorize("hasPermission('visarequestexpense','search')")
    @RequestMapping(value = "/advanceSearch/page",
            method = RequestMethod.GET)
    @Searchable(fieldName = "id",
            label = "Expense ID",
            entity = VisaExpense.class,
            apiKey = "visa-expenses")
    @Searchable(fieldName = "creationDate",
            label = "Creation date",
            entity = VisaExpense.class,
            apiKey = "visa-expenses")
    @Searchable(fieldName = "employeeType",
            label = "Employee Type",
            valuesApi = "/accounting/visarequestexpense/getallemployeetypes",
            entity = VisaExpense.class,
            apiKey = "visa-expenses")
    @Searchable(fieldName = "maidContractType",
            label = "Contract Type",
            valuesApi = "/accounting/visarequestexpense/getallmaidcontracttypes",
            entity = VisaExpense.class,
            apiKey = "visa-expenses")
    @Searchable(fieldName = "employeeName",
            label = "Name",
            entity = VisaExpense.class,
            apiKey = "visa-expenses")
    @Searchable(fieldName = "paymentType",
            label = "Type of payment",
            valuesApi = "/accounting/visarequestexpense/getallpaymenttypes",
            entity = VisaExpense.class,
            apiKey = "visa-expenses")
    @Searchable(fieldName = "amount",
            label = "Amount",
            entity = VisaExpense.class,
            apiKey = "visa-expenses")
    @Searchable(fieldName = "referenceNumber",
            label = "Reference Number",
            entity = VisaExpense.class,
            apiKey = "visa-expenses")
    @Searchable(fieldName = "status",
            label = "Status",
            valuesApi = "/accounting/visarequestexpense/getallexpensestatuses",
            entity = VisaExpense.class,
            apiKey = "visa-expenses")
    public ResponseEntity<?> advanceSearch(Pageable pageable) throws Exception {

        return new ResponseEntity<>(
                visaExpenseService.getSearchData(CurrentRequest.getSearchFilter(), pageable),
                HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('visarequestexpense','search')")
    @RequestMapping(value = "/advanceSearch/csv",
            method = RequestMethod.GET)
    public void downloadAttachmentAdvanceSearch(
            @RequestParam(name = "limit", required = false) Integer limit,
            HttpServletResponse response) {

        generateCsvFile(visaExpenseService.getCsvData(CurrentRequest.getSearchFilter()), response);
    }

    private void generateCsvFile(
            List<VisaRequestExpenseCSV> visaRequestExpenses, HttpServletResponse response) {

        InputStream is = null;
        try {

            String[] namesOrdared = {
                    "expenseName", "creationdate", "employeeType", "contractType", "name",
                    "typeOfPayment", "description", "amount", "fromBucket", "expense", "referenceNumber"};
            is = generateCsv(visaRequestExpenses, VisaRequestExpensesCsvProjection.class, namesOrdared);
            createDownloadResponse(response, "VisaRequestExpenses.csv", is);
        } catch (Exception ex) {
            logger.log(Level.SEVERE,
                    ex.getMessage(),
                    ex);
        } finally {
            StreamsUtil.closeStream(is);
        }
    }

    public List<VisaExpense> findVisaExpensesById(Long searchId){

        List<VisaExpense> VisaExpenses = new ArrayList<>();

        CancelRequestExpense cancelRequestExpense = cancelVisaRequestExpenseRepository.findOne(searchId);
        if (cancelRequestExpense != null)
            VisaExpenses.add(cancelRequestExpense);

        NewRequestExpense newRequestExpense = newVisaRequestExpenseRepository.findOne(searchId);
        if (newRequestExpense != null)
            VisaExpenses.add(newRequestExpense);

        RenewRequestExpense renewRequestExpense = renewVisaRequestExpenseRepository.findOne(searchId);
        if (renewRequestExpense != null)
            VisaExpenses.add(renewRequestExpense);

        RepeatEIDRequestExpense repeatEIDRequestExpense = repeatEIDExpenseRepository.findOne(searchId);
        if (repeatEIDRequestExpense != null)
            VisaExpenses.add(repeatEIDRequestExpense);

        ContractModificationExpense contractModificationExpense = contractModificationExpenseRepository.findOne(searchId);
        if (contractModificationExpense != null)
            VisaExpenses.add(contractModificationExpense);

        ModifyVisaRequestExpense modifyVisaRequestExpense = modifyVisaRequestExpenseRepository.findOne(searchId);
        if (modifyVisaRequestExpense != null)
            VisaExpenses.add(modifyVisaRequestExpense);

        return VisaExpenses;
    }

    public void saveVisaExpenseByType(String visaRequestExpenseType, VisaExpense expense){
        logger.log(Level.SEVERE, "saveVisaExpenseByType visaRequestExpenseType = " + visaRequestExpenseType
                + " visaRequestExpenseID = " + expense.getId());

        switch (visaRequestExpenseType.toLowerCase()) {
            case "newrequestexpense":
                newVisaRequestExpenseRepository.save((NewRequestExpense) expense);
                break;
            case "renewrequestexpense":
                renewVisaRequestExpenseRepository.save((RenewRequestExpense) expense);
                break;
            case "cancelrequestexpense":
                cancelVisaRequestExpenseRepository.save((CancelRequestExpense) expense);
                break;
            case "repeateidrequestexpense":
                repeatEIDExpenseRepository.save((RepeatEIDRequestExpense) expense);
                break;
            case "contractmodificationexpense":
                contractModificationExpenseRepository.save((ContractModificationExpense) expense);
                break;
            case "modifyvisarequestexpense":
                modifyVisaRequestExpenseRepository.save((ModifyVisaRequestExpense) expense);
                break;
        }
    }

    public VisaExpense getVisaExpenseByType(String visaRequestExpenseType, Long visaRequestExpenseID){
        logger.log(Level.SEVERE, "getVisaExpenseByType visaRequestExpenseType = " + visaRequestExpenseType
                + " visaRequestExpenseID = " + visaRequestExpenseID);

        switch (visaRequestExpenseType.toLowerCase()) {
            case "newrequestexpense":
                return newVisaRequestExpenseRepository.findOne(visaRequestExpenseID);
            case "renewrequestexpense":
                return renewVisaRequestExpenseRepository.findOne(visaRequestExpenseID);
            case "cancelrequestexpense":
                return cancelVisaRequestExpenseRepository.findOne(visaRequestExpenseID);
            case "repeateidrequestexpense":
                return repeatEIDExpenseRepository.findOne(visaRequestExpenseID);
            case "contractmodificationexpense":
                return contractModificationExpenseRepository.findOne(visaRequestExpenseID);
            case "modifyvisarequestexpense":
                return modifyVisaRequestExpenseRepository.findOne(visaRequestExpenseID);
            default:
                return null;
        }
    }

    @PreAuthorize("hasPermission('visarequestexpense','getTotalPaidVisaExpenses')")
    @GetMapping(value = "/getTotalPaidVisaExpenses/{id}")
    public ResponseEntity<?> getTotalPaidVisaExpenses(
            @PathVariable("id") Long houseMaidId) {

        Double amount = newVisaRequestExpenseRepository.findSumTransactionAmountByHouseMaidId(houseMaidId);
        return ResponseEntity.ok(new HashMap<String, Object>() {{
            put("totalAmount", amount != null ? amount : 0D);
        }});
    }
}
