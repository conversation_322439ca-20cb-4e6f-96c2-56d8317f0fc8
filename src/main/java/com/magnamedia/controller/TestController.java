package com.magnamedia.controller;

import com.magnamedia.core.Setup;
import com.magnamedia.core.controller.BaseController;
import com.magnamedia.core.entity.Attachment;
import com.magnamedia.core.entity.Template;
import com.magnamedia.core.helper.chatai.ChatAIRequestBuilder;
import com.magnamedia.core.repository.TemplateRepository;
import com.magnamedia.core.services.chatai.ChatAIService;
import com.magnamedia.extra.Utils;
import com.magnamedia.helper.DateUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.logging.Logger;

@RestController
@RequestMapping("/test")
public class TestController extends BaseController {

    private static final Logger logger = Logger.getLogger(TestController.class.getName());

    @Autowired
    private ChatAIService chatAIService;
    @Autowired
    private Utils utils;

    @PostMapping("/callGptPrompt/{name}")
    public ResponseEntity<?> callGptPrompt(
            @PathVariable("name") String templateName,
            @RequestParam("photo") MultipartFile photo) throws Exception {

        if (photo == null) return badRequestResponse();
        Template template = Setup.getRepository(TemplateRepository.class).findByNameIgnoreCase(templateName);
        if (template == null) return badRequestResponse();

        Attachment a = utils.getAttachmentFromObject(photo, "temp_callGptPrompt");

        String result = chatAIService.sendToChatGPT(new ChatAIRequestBuilder()
                .template(template)
                .templateParam(new HashMap<String, String>() {{
                    put("img","img");
                }})
                .attachments(new HashMap<String, Attachment>() {{
                    put("img", a);
                }}));

        return ResponseEntity.ok(result);
    }

    @RequestMapping(path = "/testDateUtil",
            method = RequestMethod.GET)
    @ResponseBody
    public ResponseEntity<?> testDateUtil() throws Exception
    {

        return new ResponseEntity<>(DateUtil.getStartOfCurrentWeek(), HttpStatus.OK);
    }
}
