package com.magnamedia.controller;

import com.magnamedia.core.Setup;
import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.repository.BaseRepositoryParent;
import com.magnamedia.entity.*;
import com.magnamedia.extra.AccountingPage;
import com.magnamedia.extra.VisaStatementTransactionType;
import com.magnamedia.module.type.TransactionEntityType;
import com.magnamedia.repository.*;
import com.magnamedia.service.VisaExpenseService;
import com.magnamedia.service.VisaStatementService;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@RequestMapping("/visaStatementTransaction")
@RestController
public class VisaStatementTransactionController extends BaseRepositoryController<VisaStatementTransaction> {

    @Autowired
    private VisaStatementTransactionRepository statementTransactionRepository;
    @Autowired
    private VisaStatementRepository visaStatementRepository;
    @Autowired
    private VisaExpenseService expenseService;
    @Autowired
    private VisaStatementService visaStatementService;

    @Override
    public BaseRepositoryParent<VisaStatementTransaction> getRepository() {
        return statementTransactionRepository;
    }

    @PreAuthorize("hasPermission('visaStatementTransaction','getAllTransaction')")
    @RequestMapping(value = "/getAllTransaction/{id}", method = RequestMethod.GET)
    @Transactional
    public ResponseEntity<?> getAllTransaction(@PathVariable("id") VisaStatement visaStatement) {
        SelectQuery query = new SelectQuery(VisaStatementTransaction.class);
        query.filterBy("statement", "=", visaStatement);
        query.sortBy("type", false);
        return new ResponseEntity<>(query.execute(), HttpStatus.OK);
    }


    @PreAuthorize("hasPermission('visaStatementTransaction','getMatchedTransaction')")
    @RequestMapping(value = "/getMatchedTransaction/{id}", method = RequestMethod.GET)
    public ResponseEntity<?> getMatchedTransaction(@PathVariable("id") VisaStatement visaStatement, Pageable page) {
        SelectQuery query = new SelectQuery(VisaStatementTransaction.class);
        query.filterBy("statement", "=", visaStatement);
        query.filterBy("type", "=", VisaStatementTransactionType.Matched);

        return new ResponseEntity<>(query.execute(page), HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('visaStatementTransaction','getMissingFromERPTransaction')")
    @RequestMapping(value = "/getMissingFromERPTransaction/{id}", method = RequestMethod.GET)
    public ResponseEntity<?> getMissingFromERPTransaction(@PathVariable("id") VisaStatement visaStatement, Pageable page) {
        SelectQuery query = new SelectQuery(VisaStatementTransaction.class);
        query.filterBy("statement", "=", visaStatement);
        query.filterBy("type", "=", VisaStatementTransactionType.MissingFromERP);

        return new ResponseEntity<>(query.execute(page), HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('visaStatementTransaction','getMissingFromStatementTransaction')")
    @RequestMapping(value = "/getMissingFromStatementTransaction/{id}", method = RequestMethod.GET)
    public ResponseEntity<?> getMissingFromStatementTransaction(@PathVariable("id") VisaStatement visaStatement, Pageable page) {

        Page<Object[]> r = Setup.getRepository(NewVisaRequestExpenseRepository.class)
                .getVisaExpenseMissingFromStatement(
                        visaStatement.getId(), visaStatement.getStart(),
                        new DateTime(visaStatement.getEnd()).withTimeAtStartOfDay().plusDays(1).minusMillis(1).toDate(),
                        page);
        List<VisaStatementTransaction> l = r.getContent()
                .stream()
                .map(x -> new VisaStatementTransaction(x, visaStatement, null))
                .collect(Collectors.toList());

        return ResponseEntity.ok(new AccountingPage(l, page, r.getTotalElements(), null));
    }

    @PreAuthorize("hasPermission('visaStatementTransaction','getSameReferenceNumberButDifferentAmountTransaction')")
    @GetMapping(value = "/getSameReferenceNumberButDifferentAmountTransaction/{id}")
    public ResponseEntity<?> getSameReferenceNumberButDifferentAmountTransaction(@PathVariable("id") VisaStatement visaStatement, Pageable page) {
        SelectQuery query = new SelectQuery(VisaStatementTransaction.class);
        query.filterBy("statement", "=", visaStatement);
        query.filterBy("type", "=", VisaStatementTransactionType.SameReferenceNumberButDifferentAmount);

        return new ResponseEntity<>(query.execute(page), HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('visaStatementTransaction','dismiss')")
    @PostMapping(value = "/dismiss/{id}")
    @Transactional
    public ResponseEntity<?> dismissVisaRequestExpenseList(
            @PathVariable("id") VisaStatement visaStatement,
            @RequestBody List<Map<String, Object>> visaExpenses) {

        visaExpenses.forEach(v -> expenseService.dismissVisaRequest((String) v.get("type"), Long.valueOf(v.get("id").toString())));
        refreshStatement(visaStatement);

        return ResponseEntity.ok("Done.");
    }

    @PreAuthorize("hasPermission('visaStatementTransaction','wrongMatch')")
    @GetMapping(value = "/wrongMatch/{id}")
    @Transactional
    public ResponseEntity<?> wrongMatch(@PathVariable("id") VisaStatementTransaction statementTransaction) {
        statementTransaction.setType(VisaStatementTransactionType.MissingFromERP);
        statementTransaction.initObj();
        statementTransactionRepository.save(statementTransaction);

        return new ResponseEntity<>("Done.", HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('visaStatementTransaction','rematch')")
    @PostMapping(value = "/rematch")
    @Transactional
    public ResponseEntity<?> rematch(@RequestBody List<Long> ids) {

        for (Long id : ids) {
            VisaStatementTransaction statementTransaction = statementTransactionRepository.findOne(id);
            List<Object[]> visaExpense = Setup.getRepository(NewVisaRequestExpenseRepository.class).
                    findByExpenseReferenceNumberAndCreationDate(statementTransaction.getReferenceNumber(), null, null);
            if (visaExpense.isEmpty()) continue;


            statementTransaction.fillVisaStatementTransactionInfo(visaExpense.get(0), statementTransaction.getStatement(), statementTransaction.getAmount());
            statementTransaction.setType(((Double)statementTransaction.getAmount()).equals((visaStatementService.getErpFullAmount(visaExpense.get(0)))) ?
                    VisaStatementTransactionType.Matched :
                    VisaStatementTransactionType.SameReferenceNumberButDifferentAmount);

            statementTransactionRepository.save(statementTransaction);
            refreshStatement(statementTransaction.getStatement());
        }

        return new ResponseEntity<>("Done.", HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('visaStatementTransaction','confirm')")
    @PostMapping(value = "/confirm/{id}")
    @Transactional
    public ResponseEntity<?> confirm(
            @PathVariable("id") VisaStatementTransaction visaStatementTransaction,
            @RequestBody Transaction transaction) {

        visaStatementTransaction.setFinished(true);
        visaStatementTransaction.setExpense(transaction.getExpense());
        visaStatementTransaction.setDescription(transaction.getDescription());
        visaStatementTransaction.setFromBucket(transaction.getFromBucket());
        visaStatementTransaction.setExpense(transaction.getExpense());

        ResponseEntity<?> response = Setup.getApplicationContext().getBean(VisaRequestExpenseController.class)
                .addVisaRequestExpenseTransaction(visaStatementTransaction.getVisaRequestExpenseID(),
                        visaStatementTransaction.getVisaExpenseType(), transaction);

        transaction = Setup.getRepository(TransactionRepository.class).findOne(transaction.getId());
        visaStatementTransaction.setTransaction(transaction);
        statementTransactionRepository.save(visaStatementTransaction);

        VisaStatement statement = visaStatementTransaction.getStatement();

        refreshStatement(statement);

        return response;
    }

    @PreAuthorize("hasPermission('visaStatementTransaction','confirmSelected')")
    @PostMapping(value = "/confirmSelected")
    @Transactional
    public ResponseEntity<?> confirmSelected(@RequestBody Map<Long, Transaction> body) {
        Set<Long> ids = body.keySet();

        for (Long id : ids) {
            Transaction transaction = body.get(id);
            VisaStatementTransaction visaStatementTransaction = statementTransactionRepository.findOne(id);
            confirm(visaStatementTransaction, transaction);
        }

        return new ResponseEntity<>("Done.", HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('visaStatementTransaction','fixSelectedERPAmount')")
    @PostMapping(value = "/fixSelectedERPAmount")
    @Transactional
    public ResponseEntity<?> fixSelectedERPAmount(@RequestBody List<Long> ids) {
        for (Long id : ids) {
            VisaStatementTransaction visaStatementTransaction = statementTransactionRepository.findOne(id);
            double newAmount = visaStatementTransaction.getAmount();

            VisaExpense expense = expenseService.getVisaExpenseByType(
                    visaStatementTransaction.getVisaExpenseType(), visaStatementTransaction.getVisaRequestExpenseID());
            expense.setAmount(newAmount);
            expense.setCharge(null);
            expense.setVatCharge(null);
            expenseService.saveVisaExpenseByType(visaStatementTransaction.getVisaExpenseType(), expense);

            visaStatementTransaction.setType(VisaStatementTransactionType.Matched);
            visaStatementTransaction = statementTransactionRepository.save(visaStatementTransaction);

            refreshStatement(visaStatementTransaction.getStatement());
        }

        return new ResponseEntity<>("Done.", HttpStatus.OK);
    }

    public VisaStatement refreshStatement(VisaStatement statement) {
        if (statement == null) return null;

        if (statement.getCanBeDeleted()) {
            statement.setCanBeDeleted(Boolean.FALSE);
            statement = visaStatementRepository.save(statement);
        }

        return statement;
    }

    @PreAuthorize("hasPermission('visaStatementTransaction','acc8358FixTransactions')")
    @GetMapping(value = "/acc8358FixTransactions")
    @Transactional
    public ResponseEntity<?> acc8358FixTransactions() {
        TransactionRepository transactionRepository = Setup.getRepository(TransactionRepository.class);
        HousemaidTransactionRepository housemaidTransactionRepository = Setup.getRepository(HousemaidTransactionRepository.class);

        SelectQuery<VisaStatementTransaction> q = new SelectQuery<>(VisaStatementTransaction.class);
        q.filterBy("officeStaff", "is not null", null);
        q.filterBy("transaction.transactionType", "=", TransactionEntityType.HOUSEMAID);

        List<VisaStatementTransaction> l = q.execute();
        for (VisaStatementTransaction v : l) {
            Transaction t = v.getTransaction();
            t.setTransactionType(TransactionEntityType.OFFICE_STAFF);
            OfficeStaffTransaction o = new OfficeStaffTransaction();
            o.setTransaction(t);
            o.setOfficeStaff(v.getOfficeStaff());
            t.setOfficeStaffs(Collections.singletonList(o));
            t.setPassInitLists(true);
            transactionRepository.save(t);

            housemaidTransactionRepository.findByTransaction(t)
                    .forEach(housemaidTransactionRepository::delete);
        }

        return ResponseEntity.ok("done");
    }
}
