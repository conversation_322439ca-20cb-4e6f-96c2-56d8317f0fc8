package com.magnamedia.service;

import com.magnamedia.controller.CollectionFlowLogController;
import com.magnamedia.controller.DirectDebitController;
import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.entity.Template;
import com.magnamedia.core.helper.HistorySelectQuery;
import com.magnamedia.core.helper.TemplateUtil;
import com.magnamedia.entity.*;
import com.magnamedia.entity.workflow.ClientRefundToDo;
import com.magnamedia.entity.workflow.DirectDebitRejectionToDo;
import com.magnamedia.entity.workflow.FlowEventConfig;
import com.magnamedia.entity.workflow.FlowSubEventConfig;
import com.magnamedia.extra.ContractScheduleForTerminationUtils;
import com.magnamedia.extra.DDUtils;
import com.magnamedia.extra.Utils;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.helper.PicklistHelper;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.CmOngoingCollectionFlowsTemplates.CmOngoingCollectionFlowsTemplateCode;
import com.magnamedia.module.type.*;
import com.magnamedia.repository.*;
import com.magnamedia.workflow.newversion.services.graphicdesigner.GraphicToDoType;
import com.magnamedia.workflow.type.ClientRefundStatus;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigInteger;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Haj Hussein <<EMAIL>>
 * Created At 3/6/2022
 **/

@Service
public class CollectionFlowLogService {
    private static final Logger logger =
            Logger.getLogger(CollectionFlowLogService.class.getName());

    @Autowired
    private CollectionFlowLogRepository collectionFlowLogRepository;
    @Autowired
    private CollectionFlowJobHistoryRepository collectionFlowJobHistoryRepository;
    @Autowired
    private DirectDebitRejectionToDoRepository directDebitRejectionToDoRepository;
    @Autowired
    private DirectDebitRepository directDebitRepository;
    @Autowired
    private PaymentRepository paymentRepository;
    @Autowired
    private AccountingEntityPropertyRepository accountingEntityPropertyRepository;
    @Autowired
    private ClientRefundTodoRepository clientRefundTodoRepository;
    @Autowired
    private ContractPaymentTermRepository contractPaymentTermRepository;
    @Autowired
    private DirectDebitController directDebitController;
    @Autowired
    private GraphicDesignerTodoRepository graphicDesignerTodoRepository;
    @Autowired
    private FlowProcessorEntityRepository flowProcessorEntityRepository;
    @Autowired
    private FlowProcessorService flowProcessorService;
    @Autowired
    private CCAppContentService ccAppContentService;
    @Autowired
    private CCAppService ccAppService;

    public void generateCollectionFlowLog (String flowType, Contract contract, Long relatedToId, String relatedToEntity, Date triggerDate, Date idleSinceDate, CollectionFlowStatus status, String notes) {
        generateCollectionFlowLog(flowType, contract,  relatedToId, relatedToEntity, triggerDate, idleSinceDate, status, notes, false, false);
    }

    //this is the main function
    public void generateCollectionFlowLog (String flowType, Contract contract, Long relatedToId, String relatedToEntity, Date triggerDate, Date idleSinceDate, CollectionFlowStatus status, String notes, boolean ended, Boolean rejectionTodoIsRescheduledWhenEnded) {
        CollectionFlowLog collectionFlowLog = new CollectionFlowLog(
                PicklistHelper.getItem(AccountingModule.PICKLIST_COLLECTION_FLOW_TYPE, flowType),
                contract,
                relatedToId,
                relatedToEntity,
                triggerDate,
                idleSinceDate,
                status,
                notes
        );
        collectionFlowLog.setEnded(ended);
        collectionFlowLog.setRejectionTodoIsRescheduledWhenEnded(rejectionTodoIsRescheduledWhenEnded);
        collectionFlowLogRepository.save(collectionFlowLog);
    }

    public void generateCollectionFlowLogForDdRejection (String flowType, Contract contract, DirectDebitRejectionToDo rejectionToDo) {
        String flowNotes = getCollectionFlowNotesFromRejectionCategory(rejectionToDo.getLastRejectCategory());
        DirectDebitRejectionToDoType rejectionToDoType = rejectionToDo.getTaskName() != null && !rejectionToDo.getTaskName().isEmpty() ?
                DirectDebitRejectionToDoType.valueOf(rejectionToDo.getTaskName()) : null;
        CollectionFlowStatus flowStatus = null;
        Date idleSince = null;
        DirectDebit directDebit = rejectionToDo.getLastDirectDebit();
        
        if (directDebit != null) {
            if (directDebit.getCategory().equals(DirectDebitCategory.A)) { // case DDA we only need M Status
                switch (directDebit.getMStatus()) {
                    case IN_COMPLETE:
                        flowStatus = graphicDesignerTodoRepository.existsByContractIdAndToDoTypeAndCompletedFalse(contract.getId(), GraphicToDoType.ACCOUNTING_DIGITALIZE_CLIENT_SIGNATURE) ?
                                CollectionFlowStatus.PENDING_BUSINESS_USER_ACTION :
                                CollectionFlowStatus.PENDING_CLIENT_RESPONSE;
                        break;
                    case PENDING_DATA_ENTRY:
                        flowStatus = CollectionFlowStatus.PENDING_BUSINESS_USER_ACTION;
                        break;
                    case PENDING:
                        boolean fileSent = directDebit.getDirectDebitFiles().stream()
                                .anyMatch(directDebitFile -> directDebitFile.getStatus().equals(DirectDebitFileStatus.SENT));

                        flowStatus = fileSent ? CollectionFlowStatus.PENDING_BANK_RESPONSE :
                                CollectionFlowStatus.PENDING_BUSINESS_USER_ACTION;
                        break;
                    default:
                        break;
                }
                
                idleSince = directDebit.getLastModificationDate();
            } else { // case DDB we need to check both M Status and Status
                if (directDebit.getStatus().equals(DirectDebitStatus.IN_COMPLETE)) {
                    flowStatus = graphicDesignerTodoRepository.existsByContractIdAndToDoTypeAndCompletedFalse(contract.getId(), GraphicToDoType.ACCOUNTING_DIGITALIZE_CLIENT_SIGNATURE) ?
                            CollectionFlowStatus.PENDING_BUSINESS_USER_ACTION :
                            CollectionFlowStatus.PENDING_CLIENT_RESPONSE;

                } else if (directDebit.getStatus().equals(DirectDebitStatus.PENDING_DATA_ENTRY)) {
                    flowStatus = CollectionFlowStatus.PENDING_BUSINESS_USER_ACTION;
                } else if (directDebit.getStatus().equals(DirectDebitStatus.CONFIRMED) &&
                        directDebit.getMStatus().equals(DirectDebitStatus.PENDING)) {

                    boolean manualDDsSentToBank = directDebit.getDirectDebitFiles().stream()
                            .anyMatch(ddf -> ddf.getDdMethod().equals(DirectDebitMethod.MANUAL) &&
                                    ddf.getStatus().equals(DirectDebitFileStatus.SENT));

                    flowStatus = manualDDsSentToBank ? CollectionFlowStatus.PENDING_BANK_RESPONSE :
                            CollectionFlowStatus.PENDING_BUSINESS_USER_ACTION;
                } else if (directDebit.getStatus().equals(DirectDebitStatus.PENDING) &&
                        directDebit.getMStatus().equals(DirectDebitStatus.CONFIRMED)) {

                    boolean automaticDDsSentToBank = directDebit.getDirectDebitFiles().stream()
                            .anyMatch(ddf -> ddf.getDdMethod().equals(DirectDebitMethod.AUTOMATIC) &&
                                    ddf.getStatus().equals(DirectDebitFileStatus.SENT));

                    flowStatus = automaticDDsSentToBank ? CollectionFlowStatus.PENDING_BANK_RESPONSE :
                            CollectionFlowStatus.PENDING_BUSINESS_USER_ACTION;

                } else if (directDebit.getStatus().equals(DirectDebitStatus.PENDING)) {
                    boolean fileSent = directDebit.getDirectDebitFiles().stream()
                            .anyMatch(directDebitFile -> directDebitFile.getStatus().equals(DirectDebitFileStatus.SENT));

                    flowStatus = fileSent ? CollectionFlowStatus.PENDING_BANK_RESPONSE :
                            CollectionFlowStatus.PENDING_BUSINESS_USER_ACTION;
                }

                idleSince = directDebit.getLastModificationDate();
            }
        }

        if (flowStatus == null)
            flowStatus = getCollectionFlowStatusFromRejectionToDoType(rejectionToDoType);

        if (rejectionToDoType != null && rejectionToDoType.equals(DirectDebitRejectionToDoType.WAITING_RE_SCHEDULE_B)) //if the current step is WAITING_RE_SCHEDULE_B consider the flow as ended
            generateCollectionFlowLog(flowType, contract, rejectionToDo.getId(), rejectionToDo.getEntityType(), rejectionToDo.getCreationDate(), idleSince != null ? idleSince : rejectionToDo.getCreationDate(), flowStatus, flowNotes, true, true);
        else
            generateCollectionFlowLog(flowType, contract, rejectionToDo.getId(), rejectionToDo.getEntityType(), rejectionToDo.getCreationDate(), idleSince != null ? idleSince : rejectionToDo.getCreationDate(), flowStatus, flowNotes);
    }

    public void generateCollectionFlowLogForAfterCash (ContractPaymentTerm contractPaymentTerm) {
        if (collectionFlowLogRepository.existsByFlowTypeAndContractAndEndedFalse(
                PicklistHelper.getItem(AccountingModule.PICKLIST_COLLECTION_FLOW_TYPE,
                        CollectionFlowLogController.PICKLISTITEM_IPAM_FLOW_CODE),
                contractPaymentTerm.getContract())) return;

        generateCollectionFlowLog(CollectionFlowLogController.PICKLISTITEM_IPAM_FLOW_CODE,
                contractPaymentTerm.getContract(), contractPaymentTerm.getId(),
                contractPaymentTerm.getEntityType(), new Date(), new Date(),
                CollectionFlowStatus.PENDING_CLIENT_RESPONSE, "");
    }

    //ACC-4744
    public void generateCollectionFlowLogForReminderFlow(ContractPaymentTerm contractPaymentTerm) {
        CollectionFlowLog collectionFlowLog = collectionFlowLogRepository
                .findTopByFlowTypeAndRelatedToIdAndRelatedToEntityAndContractAndEndedFalse(
                        PicklistHelper.getItem(AccountingModule.PICKLIST_COLLECTION_FLOW_TYPE,
                        CollectionFlowLogController.PICKLISTITEM_ONLINE_PAYMENTS_REMINDER_FLOW_CODE),
                        contractPaymentTerm.getId(), contractPaymentTerm.getEntityType(), contractPaymentTerm.getContract());
        if (collectionFlowLog == null)
            generateCollectionFlowLog(CollectionFlowLogController.PICKLISTITEM_ONLINE_PAYMENTS_REMINDER_FLOW_CODE,
                    contractPaymentTerm.getContract(), contractPaymentTerm.getId(),
                    contractPaymentTerm.getEntityType(), new Date(), new Date(), CollectionFlowStatus.PENDING_CLIENT_RESPONSE, "");
    }

    public void generateAllCollectionFlowLogs () {
        CollectionFlowJobHistory collectionFlowJobHistory = getNewCollectionFlowHistory();
        try {
            generateAllCollectionFlowLogs(collectionFlowJobHistory);
            collectionFlowJobHistory.setStatus(CollectionJobStatus.SUCCEED);
        } catch (Exception e) {
            logger.log(Level.SEVERE, "Exception while generateAllCollectionFlowLogs for collectionFlowJobHistory id# " + collectionFlowJobHistory.getId() + " ," + e.getMessage());
            collectionFlowJobHistory.setStatus(CollectionJobStatus.FAILED);
            collectionFlowJobHistory.setError(e.getMessage());
        }
        collectionFlowJobHistoryRepository.save(collectionFlowJobHistory);
    }

    @Transactional //this must be transactional, so if any method failed then we set the collection job history record as failed and rollback all generated logs
    public void generateAllCollectionFlowLogs (CollectionFlowJobHistory collectionFlowJobHistory) {
        Date calculateFrom = collectionFlowJobHistory.getCalculateFrom();
        Date calculateTo = collectionFlowJobHistory.getCalculateTo();

        //1- Bounced Payments
        generateBouncedPaymentsLogs(calculateFrom, calculateTo);

        //2- DD Rejection
        generateDDREjectionsLogs(calculateFrom, calculateTo);

        //3- Incomplete flow / Data entry rejection
        generateIncompleteFlowDataEntryRejectionLogs(calculateFrom, calculateTo);

        //4- Switching Nationality
        generateSwitchingNationalityLogs(calculateFrom, calculateTo);

        //5- Switching Bank Info
        generateSwitchingBankInfoLogs(calculateFrom, calculateTo);

        //6- Refund
        generateRefundLogs(calculateFrom, calculateTo);

        //7- After Cash
        generateAfterCashLogs();

        //8- Online payments reminder flow
        generateReminderFlowLogs();

        //9- Client paying via credit card
        generateClientPayingViaCreditCardLogs();

        // 10- One month agreement
        generateOneMonthAgreementLogs();

        // 11- Incomplete Flow Missing Bank Info
        generateIncompleteFlowMissingBankInfo();

        // 12- Payment Expiry
        generatePaymentExpiryLogs();

        // 13- Extension Flow
        generateExtensionFlowLogs();
    }

    public void generateBouncedPaymentsLogs (Date calculateFrom, Date calculateTo) {
        List<Payment> currentBouncedPayments = paymentRepository.findByStatus(PaymentStatus.BOUNCED);
        for (Payment bouncedPayment : currentBouncedPayments) {
            HistorySelectQuery<Payment> bouncedPaymentQuery = new HistorySelectQuery<>(Payment.class);
            bouncedPaymentQuery.filterByChanged("status");
            bouncedPaymentQuery.filterBy("status", "=", PaymentStatus.BOUNCED);
            bouncedPaymentQuery.filterBy("id", "=", bouncedPayment.getId());
            bouncedPaymentQuery.sortBy("lastModificationDate", false);
            bouncedPaymentQuery.setLimit(1);
            
            List<Payment> bouncedPaymentHistories = bouncedPaymentQuery.execute();
            
            if (!bouncedPaymentHistories.isEmpty()){
                Payment paymentHistory = bouncedPaymentHistories.get(0);
                //the payment became bounced during our time frame
                if(paymentHistory.getLastModificationDate().after(calculateFrom) && paymentHistory.getLastModificationDate().compareTo(calculateTo) <= 0) {
                    CollectionFlowStatus flowStatus = null;
                    Date idleSince = null;
                    DirectDebit directDebit = bouncedPayment.getDirectDebit();
                    List<DirectDebitStatus> ddStatuses = Arrays.asList(DirectDebitStatus.IN_COMPLETE, DirectDebitStatus.PENDING_DATA_ENTRY, DirectDebitStatus.PENDING);

                    //check the status of related DD
                    if (directDebit != null && directDebit.getDirectDebitFiles() != null && directDebit.getDirectDebitFiles().stream().anyMatch(ddf -> ddf.getDdMethod().equals(DirectDebitMethod.MANUAL))) {
                        OUTER:
                        for (DirectDebitFile ddf : directDebit.getDirectDebitFiles().stream().filter(ddf -> ddf.getDdMethod().equals(DirectDebitMethod.MANUAL) && ddStatuses.contains(ddf.getDdStatus()))
                                .collect(Collectors.toList())) {
                            switch (ddf.getDdStatus()) {
                                case IN_COMPLETE:
                                    if (graphicDesignerTodoRepository.existsByContractIdAndToDoTypeAndCompletedFalse(bouncedPayment.getContract().getId(), GraphicToDoType.ACCOUNTING_DIGITALIZE_CLIENT_SIGNATURE))
                                        flowStatus = CollectionFlowStatus.PENDING_BUSINESS_USER_ACTION;
                                    else
                                        flowStatus = CollectionFlowStatus.PENDING_CLIENT_RESPONSE;
                                    idleSince = ddf.getLastModificationDate();
                                    break OUTER;
                                case PENDING_DATA_ENTRY:
                                    flowStatus = CollectionFlowStatus.PENDING_BUSINESS_USER_ACTION;
                                    idleSince = ddf.getLastModificationDate();
                                    break OUTER;
                                case PENDING:
                                    if (directDebit.getDirectDebitFiles().stream().anyMatch(directDebitFile -> directDebitFile.getStatus().equals(DirectDebitFileStatus.SENT)))
                                        flowStatus = CollectionFlowStatus.PENDING_BANK_RESPONSE;
                                    else
                                        flowStatus = CollectionFlowStatus.PENDING_BUSINESS_USER_ACTION;
                                    idleSince = ddf.getLastModificationDate();
                                    break OUTER;
                                default:
                                    idleSince = ddf.getLastModificationDate();
                                    break;
                            }
                        }
                        // if still null then the DD is confirmed, and we need to check if the manual dd was sent to bank or not.
                        if(flowStatus == null) {
                            //if not sent to bank then it's still waiting for Business user
                            if (!bouncedPayment.getSentToBankByMDD()){
                                flowStatus = CollectionFlowStatus.PENDING_BUSINESS_USER_ACTION;
                                idleSince = idleSince != null && idleSince.compareTo(bouncedPayment.getLastModificationDate()) >= 0 ? idleSince : bouncedPayment.getLastModificationDate();
                            } else {
                                flowStatus = CollectionFlowStatus.PENDING_BANK_RESPONSE;
                                bouncedPaymentQuery = new HistorySelectQuery<>(Payment.class);
                                bouncedPaymentQuery.filterByChanged("sentToBankByMDD");
                                bouncedPaymentQuery.filterBy("sentToBankByMDD", "=", true);
                                bouncedPaymentQuery.filterBy("id", "=", bouncedPayment.getId());
                                bouncedPaymentQuery.sortBy("lastModificationDate", false);
                                bouncedPaymentQuery.setLimit(1);
                                bouncedPaymentHistories = bouncedPaymentQuery.execute();
                                if (!bouncedPaymentHistories.isEmpty())
                                    idleSince = bouncedPaymentHistories.get(0).getLastModificationDate();
                                else
                                    idleSince = bouncedPayment.getLastModificationDate();
                            }
                        }
                    } // there is no manual DD, then we still need to create a new DD
                    else {
                        flowStatus = CollectionFlowStatus.PENDING_BUSINESS_USER_ACTION;
                        idleSince = paymentHistory.getLastModificationDate();
                    }
                    Contract contract = bouncedPayment.getContract();

                    PicklistItem reasonOfTermination = contract.getReasonOfTerminationList();
                    //if the contract is terminated don't start the flow
                    if (reasonOfTermination != null && ContractScheduleForTerminationUtils.isTerminationReasonDueBouncedPayment(reasonOfTermination.getCode()) && (contract.getIsScheduledForTermination() || contract.getScheduledDateOfTermination() != null || contract.getDateOfTermination() != null))
                        continue;

                    generateCollectionFlowLog(CollectionFlowLogController.PICKLISTITEM_BOUNCED_PAYMENT_FLOW_CODE, contract, bouncedPayment.getId(), bouncedPayment.getEntityType(), paymentHistory.getLastModificationDate(), idleSince, flowStatus, "");
                }
            }
        }
    }

    public void generateDDREjectionsLogs (Date calculateFrom, Date calculateTo) {
        List<DirectDebitRejectionToDo> directDebitRejectionToDos = directDebitRejectionToDoRepository.findInCompletedTodos(calculateFrom, calculateTo);

        for (DirectDebitRejectionToDo rejectionToDo : directDebitRejectionToDos) {
            List<DirectDebit> dds = rejectionToDo.getDirectDebits();
            Contract contract = rejectionToDo.getLastDirectDebit() != null ?
                    rejectionToDo.getLastDirectDebit().getContractPaymentTerm().getContract() : 
                    (dds != null && !dds.isEmpty() ? 
                            dds.get(0).getContractPaymentTerm().getContract() : null);
            
            generateCollectionFlowLogForDdRejection(
                    CollectionFlowLogController.PICKLISTITEM_DD_REJECTION_FLOW_CODE, contract, rejectionToDo);
        }
    }

    public void generateIncompleteFlowDataEntryRejectionLogs(Date calculateFrom, Date calculateTo) {
        List<Object[]> rejectedCPTs = contractPaymentTermRepository.findRejectedCPTsByLastModificationDate(calculateFrom, calculateTo);
        for (Object [] object : rejectedCPTs) {
            ContractPaymentTerm cpt = contractPaymentTermRepository.findOne(((BigInteger) object[0]).longValue());
            Date idleSince = (Date) object[1];
            CollectionFlowLog collectionFlowLog = collectionFlowLogRepository.findTopByFlowTypeAndRelatedToIdAndRelatedToEntityAndContractAndEndedFalse(PicklistHelper.getItem(AccountingModule.PICKLIST_COLLECTION_FLOW_TYPE, CollectionFlowLogController.PICKLISTITEM_INCOMPLETE_FLOW_DATA_ENTRY_REJECTION),
                    cpt.getId(), cpt.getEntityType(), cpt.getContract());
            if (collectionFlowLog == null) {

                generateCollectionFlowLog(CollectionFlowLogController.PICKLISTITEM_INCOMPLETE_FLOW_DATA_ENTRY_REJECTION,
                        cpt.getContract(),
                        cpt.getId(),
                        cpt.getEntityType(),
                        idleSince,
                        idleSince,
                        getIncompleteFlowStatus(cpt.getContract()),
                        "");
            }
        }
    }

    public void generateSwitchingNationalityLogs(Date calculateFrom, Date calculateTo) {

        //checking properties
        List<AccountingEntityProperty> accountingEntityProperties = accountingEntityPropertyRepository.findByKeyAndCreationDateAndDeletedFalse(
                Arrays.asList(Contract.DOWNGRADING_NATIONALITY_JOB_PASSED_WHILE_HAS_NO_MAID,
                        Contract.DOWNGRADING_NATIONALITY_DATE, Contract.UPGRADING_NATIONALITY_DATE),
                calculateFrom, calculateTo);

        for (AccountingEntityProperty property : accountingEntityProperties) {
            Contract contract = (Contract) property.getOrigin();
            if (contract.getDateOfTermination() != null) continue;

            generateCollectionFlowLog(CollectionFlowLogController.PICKLISTITEM_SWITCHING_NATIONALITY_FLOW_CODE,
                    contract, property.getId(), property.getEntityType(), property.getCreationDate(), property.getCreationDate(),
                    CollectionFlowStatus.PENDING_CLIENT_RESPONSE, "");
        }
    }

    public void generateSwitchingBankInfoLogs (Date calculateFrom, Date calculateTo) {

        //checking new CPTs second
        List<ContractPaymentTerm> newCPTs = contractPaymentTermRepository.findByReasonAndCreationDate(ContractPaymentTermReason.SWITCHING_BANK_ACCOUNT, calculateFrom, calculateTo);

        for (ContractPaymentTerm newCPT : newCPTs){
            Map<String, Object> details = getSwitchBankInfoDetails(newCPT);

            Boolean endedFlag = (Boolean) details.get("endedFlag");
            CollectionFlowStatus flowStatus = (CollectionFlowStatus) details.get("flowStatus");
            Date idleSince = (Date) details.get("idleSince");
            if (!endedFlag)
                generateCollectionFlowLog(CollectionFlowLogController.PICKLISTITEM_SWITCHING_BANK_ACCOUNT_FLOW_CODE, newCPT.getContract(), newCPT.getId(), newCPT.getEntityType(), newCPT.getCreationDate(), idleSince, flowStatus, "");
        }
    }

    public void generateRefundLogs (Date calculateFrom, Date calculateTo) {
        List<ClientRefundToDo> refundToDos = clientRefundTodoRepository.findByStatusAndCreationDate(ClientRefundStatus.PENDING, calculateFrom, calculateTo);

        for (ClientRefundToDo refundToDo : refundToDos) {
            generateCollectionFlowLog(CollectionFlowLogController.PICKLISTITEM_REFUND_FLOW_CODE, refundToDo.getContract(), refundToDo.getId(), refundToDo.getEntityType(), refundToDo.getCreationDate(), refundToDo.getTaskModifiedDate(), CollectionFlowStatus.PENDING_BUSINESS_USER_ACTION, "");
        }
    }

    public void generateAfterCashLogs () {
        FlowEventConfig flowEventConfig = Setup.getRepository(FlowEventConfigRepository.class)
                .findByName(FlowEventConfig.FlowEventName.CLIENT_PAID_CASH_NO_SIGNATURE_PROVIDED);

        Setup.getRepository(FlowProcessorEntityRepository.class)
                .findByFlowEventConfigAndStoppedFalseAndCompletedFalse(flowEventConfig)
                .forEach(flow -> generateCollectionFlowLogForAfterCash(flow.getContractPaymentTerm()));
    }

    // ACC-7244
    public void generatePaymentExpiryLogs () {

        collectionFlowLogRepository.findActivePaymentExpiryFlowsWithoutLogs()
                .forEach(f -> {
                    logger.info("flow id: " + f.getId());
                    generateCollectionFlowLog(CollectionFlowLogController.PICKLISTITEM_PAYMENT_EXPIRY_FLOW_CODE,
                            f.getContract(), f.getId(),
                            f.getEntityType(), new Date(), new Date(),
                            CollectionFlowStatus.PENDING_CLIENT_RESPONSE, "");
                });
    }

    //ACC-4744
    public void generateReminderFlowLogs() {
        FlowEventConfig flowEventConfig = Setup.getRepository(FlowEventConfigRepository.class)
                .findByName(FlowEventConfig.FlowEventName.ONLINE_CREDIT_CARD_PAYMENT_REMINDERS);
        if (flowEventConfig == null) return;
        List<FlowProcessorEntity> flowProcessorEntities = Setup.getRepository(FlowProcessorEntityRepository.class)
                .findByFlowEventConfigAndStoppedFalseAndCompletedFalse(flowEventConfig);

        for (FlowProcessorEntity flowProcessorEntity : flowProcessorEntities) {
            logger.log(Level.INFO, "flowProcessorEntity id : {0}", flowProcessorEntity.getId());
            generateCollectionFlowLogForReminderFlow(flowProcessorEntity.getContractPaymentTerm());
        }
    }

    // ACC-4715
    public void generateClientPayingViaCreditCardLogs() {

        collectionFlowLogRepository.findContractFlaggedPayingViaCreditCardWithoutLogs(Arrays.asList(
                        ContractStatus.ACTIVE, ContractStatus.PLANNED_RENEWAL, ContractStatus.POSTPONED))
                .forEach(c -> {
                    try {
                        logger.log(Level.INFO, "contract id : {0}", c.getId());
                        generateCollectionFlowLog(CollectionFlowLogController.PICKLISTITEM_CLIENT_PAYING_VIA_CREDIT_CARD_FLOW_CODE,
                                c, c.getId(),
                                c.getEntityType(), new Date(), new Date(),
                                getPayingViaCreditCardLogStatus(c), "");
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                });
    }

    // ACC-5751
    private void generateOneMonthAgreementLogs() {

        List<ContractStatus> allowedStatus = Arrays.asList(
                ContractStatus.ACTIVE, ContractStatus.PLANNED_RENEWAL, ContractStatus.POSTPONED);

        collectionFlowLogRepository.findContractFlaggedOneMonthAgreementWithoutLogs(allowedStatus)
                .forEach(c -> {
                try {
                    logger.log(Level.INFO, "contract id : {0}", c.getId());
                        generateCollectionFlowLog(CollectionFlowLogController.PICKLISTITEM_ONE_MONTH_AGREEMENT_CODE,
                                c, c.getId(),
                                c.getEntityType(), new Date(), new Date(),
                                getOneMonthAgreementLogStatus(c), "");
                } catch (Exception e) {
                    e.printStackTrace();
                }
        });
    }

    // ACC-6804
    private void generateIncompleteFlowMissingBankInfo() {

        collectionFlowLogRepository.findFlowProcessorEntityWithoutLogs(
                CollectionFlowLogController.PICKLISTITEM_INCOMPLETE_FLOW_MISSING_BANK_INFO,
                FlowEventConfig.FlowEventName.INCOMPLETE_FLOW_MISSING_BANK_INFO)
                .forEach(c -> {
                    logger.info("create new flow contract id: " + c.getId());
                    generateCollectionFlowLog(CollectionFlowLogController.PICKLISTITEM_INCOMPLETE_FLOW_MISSING_BANK_INFO,
                            c,
                            c.getId(),
                            c.getEntityType(),
                            new Date(),
                            new Date(),
                            getIncompleteFlowStatus(c),
                            "");
                });
    }

    // ACC-8954
    public void generateExtensionFlowLogs() {
        collectionFlowLogRepository.findAllFlowProcessorEntityWithoutLogs(
                CollectionFlowLogController.PICKLISTITEM_EXTENSION_FLOW, FlowEventConfig.FlowEventName.EXTENSION_FLOW)
                .forEach(flow -> {
                    try {
                        logger.info("flowProcessorEntity id : " + flow.getId());
                        generateCollectionFlowLog(CollectionFlowLogController.PICKLISTITEM_EXTENSION_FLOW,
                                flow.getContractPaymentTerm().getContract(), flow.getId(),
                                flow.getEntityType(), flow.getCreationDate(), flow.getCreationDate(),
                                CollectionFlowStatus.PENDING_CLIENT_RESPONSE, "");
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                });
    }

    private CollectionFlowStatus getOneMonthAgreementLogStatus(
            Contract c) {

        if (directDebitRepository.existsPendingDdWithDdfSent(c))
            return CollectionFlowStatus.PENDING_BANK_RESPONSE;
        if(directDebitRepository.existsPendingUserAction(c))
            return CollectionFlowStatus.PENDING_BUSINESS_USER_ACTION;
        return CollectionFlowStatus.PENDING_CLIENT_RESPONSE;
    }

    public void updateAllCurrentLogs(){
        //get all opened flow logs
        List<CollectionFlowLog> currentCollectionFlowLogList = collectionFlowLogRepository.findByEndedFalseOrderByIdDesc();
        //add Rescheduled DD Rejection flows (they are ended now but we need to check if 1 month is passed and the step of DDRejection To-do is changed)
        currentCollectionFlowLogList.addAll(collectionFlowLogRepository.findByEndedTrueAndRejectionTodoIsRescheduledWhenEndedTrue());

        List<Exception> allExceptions = new ArrayList<>();
        List<Long> flowLogsWithExceptions = new ArrayList<>();
        for (CollectionFlowLog flowLog : currentCollectionFlowLogList) {
            try {
                handleCollectionFlowLog(flowLog);
            } catch (Exception e) {
                logger.log(Level.SEVERE, "Exception while handleCollectionFlowLog for collectionFlowLog id# " + flowLog.getId() + " ," + e.getMessage());
                flowLogsWithExceptions.add(flowLog.getId());
                allExceptions.add(e);
            }
        }
    }

    public void handleCollectionFlowLog(CollectionFlowLog flowLog) {
        switch (flowLog.getFlowType().getCode()) {
            case CollectionFlowLogController.PICKLISTITEM_BOUNCED_PAYMENT_FLOW_CODE:
                handleBouncedPaymentLog(flowLog);
                break;
            case CollectionFlowLogController.PICKLISTITEM_DD_REJECTION_FLOW_CODE:
                handleDDREjectionLog(flowLog);
                break;
            case CollectionFlowLogController.PICKLISTITEM_INCOMPLETE_FLOW_DATA_ENTRY_REJECTION:
                handleIncompleteFlowDataEntryRejectionLog(flowLog);
                break;
            case CollectionFlowLogController.PICKLISTITEM_SWITCHING_NATIONALITY_FLOW_CODE:
                handleSwitchingNationalityLog(flowLog);
                break;
            case CollectionFlowLogController.PICKLISTITEM_SWITCHING_BANK_ACCOUNT_FLOW_CODE:
                handleSwitchingBankInfoLog(flowLog);
                break;
            case CollectionFlowLogController.PICKLISTITEM_REFUND_FLOW_CODE:
                handleRefundLog(flowLog);
                break;
            case CollectionFlowLogController.PICKLISTITEM_IPAM_FLOW_CODE:
                handleAfterCashLog(flowLog);
                break;
            case CollectionFlowLogController.PICKLISTITEM_ONLINE_PAYMENTS_REMINDER_FLOW_CODE:
                handleReminderFlowLog(flowLog);
                break;
            case CollectionFlowLogController.PICKLISTITEM_CLIENT_PAYING_VIA_CREDIT_CARD_FLOW_CODE:
                handleClientPayingViaCreditCardLog(flowLog);
                break;
            case CollectionFlowLogController.PICKLISTITEM_ONE_MONTH_AGREEMENT_CODE:
                handleOneMonthAgreementLog(flowLog);
                break;
            case CollectionFlowLogController.PICKLISTITEM_INCOMPLETE_FLOW_MISSING_BANK_INFO:
                handleIncompleteFlowMissingBankInfo(flowLog);
                break;
            case CollectionFlowLogController.PICKLISTITEM_PAYMENT_EXPIRY_FLOW_CODE:
                handlePaymentExpiryFlow(flowLog);
                break;
            case CollectionFlowLogController.PICKLISTITEM_EXTENSION_FLOW:
                handleExtensionFlowLog(flowLog);
                break;

        }
    }


    public void handleBouncedPaymentLog (CollectionFlowLog flowLog) {
        Payment bouncedPayment = paymentRepository.findOne(flowLog.getRelatedToId());
        Contract contract = bouncedPayment.getContract();

        PicklistItem reasonOfTermination = contract.getReasonOfTerminationList();
        if ((reasonOfTermination != null && ContractScheduleForTerminationUtils.isTerminationReasonDueBouncedPayment(reasonOfTermination.getCode()) && (contract.getIsScheduledForTermination() || contract.getScheduledDateOfTermination() != null || contract.getDateOfTermination() != null))
                || !bouncedPayment.getStatus().equals(PaymentStatus.BOUNCED) 
                || bouncedPayment.isReplaced()){
            
            flowLog.setEnded(true);
            collectionFlowLogRepository.save(flowLog);
        } else {
            CollectionFlowStatus flowStatus = null;
            Date idleSince = null;
            DirectDebit directDebit = bouncedPayment.getDirectDebit();
            List<DirectDebitStatus> ddStatuses = Arrays.asList(DirectDebitStatus.IN_COMPLETE, DirectDebitStatus.PENDING_DATA_ENTRY, DirectDebitStatus.PENDING);

            //check the status of related DD
            if (directDebit != null && directDebit.getDirectDebitFiles() != null && 
                    directDebit.getDirectDebitFiles().stream()
                            .anyMatch(ddf -> ddf.getDdMethod().equals(DirectDebitMethod.MANUAL))) {
                OUTER:
                for (DirectDebitFile ddf : directDebit.getDirectDebitFiles().stream()
                        .filter(ddf -> ddf.getDdMethod().equals(DirectDebitMethod.MANUAL) && ddStatuses.contains(ddf.getDdStatus()))
                        .collect(Collectors.toList())) {
                    
                    switch (ddf.getDdStatus()) {
                        case IN_COMPLETE:
                            if (graphicDesignerTodoRepository.existsByContractIdAndToDoTypeAndCompletedFalse(contract.getId(), GraphicToDoType.ACCOUNTING_DIGITALIZE_CLIENT_SIGNATURE))
                                flowStatus = CollectionFlowStatus.PENDING_BUSINESS_USER_ACTION;
                            else
                                flowStatus = CollectionFlowStatus.PENDING_CLIENT_RESPONSE;
                            idleSince = ddf.getLastModificationDate();
                            break OUTER;
                        case PENDING_DATA_ENTRY:
                            flowStatus = CollectionFlowStatus.PENDING_BUSINESS_USER_ACTION;
                            idleSince = ddf.getLastModificationDate();
                            break OUTER;
                        case PENDING:
                            if (directDebit.getDirectDebitFiles().stream().anyMatch(directDebitFile -> directDebitFile.getStatus().equals(DirectDebitFileStatus.SENT)))
                                flowStatus = CollectionFlowStatus.PENDING_BANK_RESPONSE;
                            else
                                flowStatus = CollectionFlowStatus.PENDING_BUSINESS_USER_ACTION;
                            idleSince = ddf.getLastModificationDate();
                            break OUTER;
                        default:
                            idleSince = ddf.getLastModificationDate();
                            break;
                    }
                }
                // if still null then the DD is confirmed, and we need to check if the manual dd was sent to bank or not.
                if(flowStatus == null) {
                    //if not sent to bank then it's still waiting for Business user
                    if (!bouncedPayment.getSentToBankByMDD()){
                        flowStatus = CollectionFlowStatus.PENDING_BUSINESS_USER_ACTION;
                        idleSince = idleSince != null && idleSince.compareTo(bouncedPayment.getLastModificationDate()) >= 0 ? idleSince : bouncedPayment.getLastModificationDate();
                    } else {
                        flowStatus = CollectionFlowStatus.PENDING_BANK_RESPONSE;
                        
                        HistorySelectQuery<Payment> bouncedPaymentQuery = new HistorySelectQuery<>(Payment.class);
                        bouncedPaymentQuery.filterByChanged("sentToBankByMDD");
                        bouncedPaymentQuery.filterBy("sentToBankByMDD", "=", true);
                        bouncedPaymentQuery.filterBy("id", "=", bouncedPayment.getId());
                        bouncedPaymentQuery.sortBy("lastModificationDate", false);
                        bouncedPaymentQuery.setLimit(1);
                        
                        List<Payment> bouncedPaymentHistories = bouncedPaymentQuery.execute();
                        
                        if (!bouncedPaymentHistories.isEmpty())
                            idleSince = bouncedPaymentHistories.get(0).getLastModificationDate();
                        else
                            idleSince = bouncedPayment.getLastModificationDate();
                    }
                }
            }

            if (flowStatus != null) {
                flowLog.setStatus(flowStatus);
                flowLog.setIdleSinceDate(idleSince);
                collectionFlowLogRepository.save(flowLog);
            }
        }
    }

    public void handleDDREjectionLog (CollectionFlowLog flowLog) {
        DirectDebitRejectionToDo directDebitRejectionToDo = directDebitRejectionToDoRepository.findOne(flowLog.getRelatedToId());
        DirectDebitRejectionToDoType rejectionToDoType = 
                directDebitRejectionToDo.getTaskName() != null && !directDebitRejectionToDo.getTaskName().isEmpty() ? 
                        DirectDebitRejectionToDoType.valueOf(directDebitRejectionToDo.getTaskName()) : null;
        Contract contract = flowLog.getContract();
        
        String notes = getCollectionFlowNotesFromRejectionCategory(directDebitRejectionToDo.getLastRejectCategory());
        flowLog.setNotes(notes);

        if (directDebitRejectionToDo.isCompleted() || directDebitRejectionToDo.isStopped()){
            flowLog.setEnded(true);
        } else if (rejectionToDoType != null && rejectionToDoType.equals(DirectDebitRejectionToDoType.WAITING_RE_SCHEDULE_B)) {
            flowLog.setStatus(CollectionFlowStatus.PENDING_BUSINESS_USER_ACTION);
            flowLog.setRejectionTodoIsRescheduledWhenEnded(true);
            flowLog.setEnded(true);
        } else {
            CollectionFlowStatus flowStatus = flowLog.getStatus();
            Date idleSince = flowLog.getIdleSinceDate();
            DirectDebit directDebit = directDebitRejectionToDo.getLastDirectDebit();
            if (directDebit != null) {
                if (directDebit.getCategory().equals(DirectDebitCategory.A)) { // case DDA we only need M Status
                    switch (directDebit.getMStatus()) {
                        case IN_COMPLETE:
                            if (graphicDesignerTodoRepository.existsByContractIdAndToDoTypeAndCompletedFalse(contract.getId(), GraphicToDoType.ACCOUNTING_DIGITALIZE_CLIENT_SIGNATURE))
                                flowStatus = CollectionFlowStatus.PENDING_BUSINESS_USER_ACTION;
                            else
                                flowStatus = CollectionFlowStatus.PENDING_CLIENT_RESPONSE;
                            break;
                        case PENDING_DATA_ENTRY:
                            flowStatus = CollectionFlowStatus.PENDING_BUSINESS_USER_ACTION;
                            break;
                        case PENDING:
                            if (directDebit.getDirectDebitFiles().stream().anyMatch(directDebitFile -> directDebitFile.getStatus().equals(DirectDebitFileStatus.SENT)))
                                flowStatus = CollectionFlowStatus.PENDING_BANK_RESPONSE;
                            else
                                flowStatus = CollectionFlowStatus.PENDING_BUSINESS_USER_ACTION;
                            break;
                        default:
                            break;
                    }
                    
                    idleSince = directDebit.getLastModificationDate();
                } else { // case DDB we need to check both M Status and Status
                    if (directDebit.getStatus().equals(DirectDebitStatus.IN_COMPLETE) && directDebit.getMStatus().equals(DirectDebitStatus.IN_COMPLETE)) {
                        if (graphicDesignerTodoRepository.existsByContractIdAndToDoTypeAndCompletedFalse(contract.getId(), GraphicToDoType.ACCOUNTING_DIGITALIZE_CLIENT_SIGNATURE))
                            flowStatus = CollectionFlowStatus.PENDING_BUSINESS_USER_ACTION;
                        else
                            flowStatus = CollectionFlowStatus.PENDING_CLIENT_RESPONSE;
                    } else if (directDebit.getStatus().equals(DirectDebitStatus.PENDING_DATA_ENTRY)) {
                        flowStatus = CollectionFlowStatus.PENDING_BUSINESS_USER_ACTION;
                    }else if (directDebit.getStatus().equals(DirectDebitStatus.CONFIRMED) &&
                            directDebit.getMStatus().equals(DirectDebitStatus.PENDING)) {
                        boolean manualDDsSentToBank = directDebit.getDirectDebitFiles().stream().anyMatch(ddf -> ddf.getDdMethod().equals(DirectDebitMethod.MANUAL) && ddf.getStatus().equals(DirectDebitFileStatus.SENT));
                        if (manualDDsSentToBank)
                            flowStatus = CollectionFlowStatus.PENDING_BANK_RESPONSE;
                        else
                            flowStatus = CollectionFlowStatus.PENDING_BUSINESS_USER_ACTION;
                    } else if (directDebit.getStatus().equals(DirectDebitStatus.PENDING) &&
                            directDebit.getMStatus().equals(DirectDebitStatus.CONFIRMED)) {
                        boolean manualDDsSentToBank = directDebit.getDirectDebitFiles().stream().anyMatch(ddf -> ddf.getDdMethod().equals(DirectDebitMethod.AUTOMATIC) && ddf.getStatus().equals(DirectDebitFileStatus.SENT));
                        if (manualDDsSentToBank)
                            flowStatus = CollectionFlowStatus.PENDING_BANK_RESPONSE;
                        else
                            flowStatus = CollectionFlowStatus.PENDING_BUSINESS_USER_ACTION;
                    } else if (directDebit.getStatus().equals(DirectDebitStatus.PENDING)) {
                        if (directDebit.getDirectDebitFiles().stream().anyMatch(directDebitFile -> directDebitFile.getStatus().equals(DirectDebitFileStatus.SENT)))
                            flowStatus = CollectionFlowStatus.PENDING_BANK_RESPONSE;
                        else
                            flowStatus = CollectionFlowStatus.PENDING_BUSINESS_USER_ACTION;
                    }
                    idleSince = directDebit.getLastModificationDate();
                }
            }
            flowLog.setStatus(flowStatus);
            flowLog.setIdleSinceDate(idleSince);

            // if the RejectionTodo was already Rescheduled so mark the flags is false
            if (flowLog.getRejectionTodoIsRescheduledWhenEnded() != null && flowLog.getRejectionTodoIsRescheduledWhenEnded()) {
                flowLog.setRejectionTodoIsRescheduledWhenEnded(false);
                flowLog.setEnded(false);
            }
        }

        collectionFlowLogRepository.save(flowLog);
    }

    public void handleIncompleteFlowDataEntryRejectionLog(CollectionFlowLog flowLog) {
        ContractPaymentTerm cpt = contractPaymentTermRepository.findOne(flowLog.getRelatedToId());

        Date idleSince = flowLog.getIdleSinceDate();
        if (cpt == null || cpt.getContract().getDateOfTermination() != null
                || cpt.getContract().getScheduledDateOfTermination() != null) {
            
            flowLog.setEnded(true);
        } else if (!cpt.getIsIBANRejected() && !cpt.getIsAccountHolderRejected() && !cpt.getIsEidRejected()) {
            boolean ddPendingDataEntry = directDebitRepository.findByContractPaymentTerm(cpt).stream()
                    .anyMatch(directDebit -> (directDebit.getCategory().equals(DirectDebitCategory.A) && 
                            directDebit.getMStatus().equals(DirectDebitStatus.PENDING_DATA_ENTRY)) || 
                            (directDebit.getCategory().equals(DirectDebitCategory.B) && 
                                    directDebit.getStatus().equals(DirectDebitStatus.PENDING_DATA_ENTRY)));

            if (ddPendingDataEntry) {
                flowLog.setStatus(CollectionFlowStatus.PENDING_BUSINESS_USER_ACTION);
                flowLog.setIdleSinceDate(cpt.getLastModificationDate());
            } else {
                flowLog.setEnded(true);
            }
        } else {
            //in case still rejected or rejected again --> reset the flow status

            if (graphicDesignerTodoRepository.existsByContractIdAndToDoTypeAndCompletedFalse(cpt.getContract().getId(), GraphicToDoType.ACCOUNTING_DIGITALIZE_CLIENT_SIGNATURE)) {
                GraphicDesignerToDo graphicDesignerToDo = graphicDesignerTodoRepository.findTopByContractIdAndToDoTypeAndCompletedFalseOrderByIdDesc(cpt.getContract().getId(), GraphicToDoType.ACCOUNTING_DIGITALIZE_CLIENT_SIGNATURE);
                idleSince = idleSince.compareTo(graphicDesignerToDo.getLastModificationDate()) < 0 ? graphicDesignerToDo.getLastModificationDate() : idleSince;
                flowLog.setStatus(CollectionFlowStatus.PENDING_BUSINESS_USER_ACTION);
            } else {
                idleSince = idleSince.compareTo(cpt.getLastModificationDate()) < 0 ? cpt.getLastModificationDate() : idleSince;

                boolean ddIncomplete = directDebitRepository.findByContractPaymentTerm(cpt).stream()
                    .anyMatch(directDebit -> (directDebit.getCategory().equals(DirectDebitCategory.A) &&
                            directDebit.getMStatus().equals(DirectDebitStatus.IN_COMPLETE)) ||
                            (directDebit.getCategory().equals(DirectDebitCategory.B) &&
                                    directDebit.getStatus().equals(DirectDebitStatus.IN_COMPLETE)));

                if(ddIncomplete) {
                    flowLog.setStatus(CollectionFlowStatus.PENDING_CLIENT_RESPONSE);
                } else {
                    flowLog.setEnded(true);
                }
            }
            
            flowLog.setIdleSinceDate(idleSince);
        }
        collectionFlowLogRepository.save(flowLog);
    }

    public void handleSwitchingNationalityLog (CollectionFlowLog flowLog) {
        AccountingEntityProperty accountingEntityProperty = accountingEntityPropertyRepository.findOne(flowLog.getRelatedToId());
        Contract contract = accountingEntityProperty != null ? (Contract) accountingEntityProperty.getOrigin() : null;

        if (accountingEntityProperty == null || accountingEntityProperty.getIsDeleted() ||
                (contract != null && contract.getDateOfTermination() != null)) {
            flowLog.setEnded(true);
            collectionFlowLogRepository.save(flowLog);
        }
    }

    public void handleSwitchingBankInfoLog (CollectionFlowLog flowLog) {
        //handle newCPT
        ContractPaymentTerm newCPT = contractPaymentTermRepository.findOne(flowLog.getRelatedToId());
        Map<String, Object> details = getSwitchBankInfoDetails(newCPT);

        Boolean endedFlag = (Boolean) details.get("endedFlag");
        CollectionFlowStatus flowStatus = (CollectionFlowStatus) details.get("flowStatus");
        Date idleSince = (Date) details.get("idleSince");

        if (endedFlag) {
            flowLog.setEnded(true);
        } else {
            flowLog.setIdleSinceDate(idleSince.compareTo(flowLog.getIdleSinceDate()) >= 0 ? idleSince : flowLog.getIdleSinceDate());
            flowLog.setStatus(flowStatus);
        }
        collectionFlowLogRepository.save(flowLog);
    }

    public void handleRefundLog (CollectionFlowLog flowLog) {
        ClientRefundToDo refundToDo = clientRefundTodoRepository.findOne(flowLog.getRelatedToId());

        if (refundToDo == null || (refundToDo.getStatus().equals(ClientRefundStatus.REJECTED)) || (refundToDo.getStatus().equals(ClientRefundStatus.PAID) && refundToDo.isCompleted() && refundToDo.isStopped()))
            flowLog.setEnded(true);
        else
            flowLog.setIdleSinceDate(refundToDo.getTaskModifiedDate());
        collectionFlowLogRepository.save(flowLog);
    }

    public void handleAfterCashLog (CollectionFlowLog flowLog) {
        ContractPaymentTerm cpt = contractPaymentTermRepository.findOne(flowLog.getRelatedToId());

        boolean flowRunning = Setup.getRepository(FlowProcessorEntityRepository.class)
                .existsByFlowEventConfig_NameAndContractPaymentTerm_ContractAndStoppedFalseAndCompletedFalse(
                        FlowEventConfig.FlowEventName.CLIENT_PAID_CASH_NO_SIGNATURE_PROVIDED,
                        cpt.getContract());

        if (!flowRunning) {
            flowLog.setEnded(true);
            collectionFlowLogRepository.save(flowLog);
        }
    }

    //ACC-4744
    public void handleReminderFlowLog(CollectionFlowLog flowLog) {
        ContractPaymentTerm cpt = contractPaymentTermRepository.findOne(flowLog.getRelatedToId());
        FlowEventConfig flowEventConfig = Setup.getRepository(FlowEventConfigRepository.class)
                .findByName(FlowEventConfig.FlowEventName.ONLINE_CREDIT_CARD_PAYMENT_REMINDERS);
        if (flowEventConfig == null) return;
        FlowProcessorEntity flowProcessorEntity = Setup.getRepository(FlowProcessorEntityRepository.class)
                .findFirstByFlowEventConfigAndContractPaymentTermAndStoppedFalseAndCompletedFalseOrderByCreationDateDesc(flowEventConfig, cpt);

        if (flowProcessorEntity == null) {
            flowLog.setEnded(true);
            collectionFlowLogRepository.save(flowLog);
        }
    }

    // ACC-4715
    public void handleClientPayingViaCreditCardLog(CollectionFlowLog flowLog) {

        if (!flowLog.getContract().isPayingViaCreditCard() ||
                flowProcessorService.existsRunningFlow(flowLog.getContract(), FlowEventConfig.FlowEventName.EXTENSION_FLOW)) {
            logger.log(Level.INFO, "log id: {0} stopped", flowLog.getId());
            flowLog.setEnded(true);
            collectionFlowLogRepository.save(flowLog);
            return;
        }

        CollectionFlowStatus status = getPayingViaCreditCardLogStatus(flowLog.getContract());
        if (status.equals(flowLog.getStatus())) return;

        flowLog.setStatus(status);
        collectionFlowLogRepository.save(flowLog);
    }

    private CollectionFlowStatus getPayingViaCreditCardLogStatus(Contract c) {

        List<FlowSubEventConfig.FlowSubEventName> subFlows = new ArrayList<>(FlowProcessorService.recurringFailureFlows);
        subFlows.add(FlowSubEventConfig.FlowSubEventName.INITIAL_FLOW_FOR_DDA);
        subFlows.add(FlowSubEventConfig.FlowSubEventName.INITIAL_FLOW_FOR_DDB);

        if (flowProcessorService.existsRunningFlow(
                c, FlowEventConfig.FlowEventName.CLIENTS_PAYING_VIA_Credit_Card, subFlows))
            return CollectionFlowStatus.PENDING_CLIENT_RESPONSE;

        boolean nextMonthPaymentReceived = paymentRepository.existsMonthlyPaymentReceivedByDate(
                c, new LocalDate().plusMonths(1).getYear(),
                new LocalDate().plusMonths(1).getMonthOfYear());
        boolean hasPendingDd = Setup.getRepository(DirectDebitFileRepository.class)
                .existsByContractPaymentTermAndDdCategoryBAndDdStatusInAndStatusSent(
                        c.getActiveContractPaymentTerm(), DirectDebitStatus.PENDING);
        logger.log(Level.INFO, "c id: {0}; nextMonthPaymentReceived: {1}; hasPendingDd: {2}",
                new Object[]{c.getId(), nextMonthPaymentReceived, hasPendingDd});

        return nextMonthPaymentReceived && hasPendingDd ?
                CollectionFlowStatus.PENDING_BANK_RESPONSE : CollectionFlowStatus.PENDING_CLIENT_RESPONSE;
    }

    // ACC-5751
    public void handleOneMonthAgreementLog(
            CollectionFlowLog flowLog) {
        List<ContractStatus> allowedStatus = Arrays.asList(
                ContractStatus.ACTIVE, ContractStatus.PLANNED_RENEWAL, ContractStatus.POSTPONED);
        if (!flowLog.getContract().isOneMonthAgreement() ||
            !allowedStatus.contains(flowLog.getContract().getStatus())) {
            logger.log(Level.INFO, "log id: {0} stopped", flowLog.getId());
            flowLog.setEnded(true);
        } else flowLog.setStatus(getOneMonthAgreementLogStatus(flowLog.getContract()));

        collectionFlowLogRepository.save(flowLog);
    }

    //ACC-7244
    public void handlePaymentExpiryFlow(CollectionFlowLog flowLog) {
        if (flowProcessorEntityRepository.existsRunningFlow(
                flowLog.getContract(), Collections.singletonList(FlowEventConfig.FlowEventName.PAYMENT_EXPIRY_FLOW))) return;

        logger.info("flow stopped id: " + flowLog.getId());
        flowLog.setEnded(true);
        collectionFlowLogRepository.save(flowLog);
    }

    // ACC-6804
    public void handleIncompleteFlowMissingBankInfo(CollectionFlowLog flowLog) {

        if (flowProcessorService.existsRunningFlow(flowLog.getContract(), FlowEventConfig.FlowEventName.INCOMPLETE_FLOW_MISSING_BANK_INFO)) {
            CollectionFlowStatus flowStatus = getIncompleteFlowStatus(flowLog.getContract());
            if (flowStatus.equals(flowLog.getStatus())) return;
            logger.info("flow status changed to: " + flowStatus +  "; contract id: " + flowLog.getContract().getId());
            flowLog.setStatus(flowStatus);
            flowLog.setIdleSinceDate(new Date());
        } else {
            logger.info("flow stopped contract id: " + flowLog.getContract().getId());
            flowLog.setEnded(true);
        }

        collectionFlowLogRepository.save(flowLog);
    }

    private CollectionFlowStatus getIncompleteFlowStatus(Contract c) {
        return c.isSigningPaperMode() &&
                graphicDesignerTodoRepository.existsByContractIdAndToDoTypeAndCompletedFalse(
                        c.getId(), GraphicToDoType.ACCOUNTING_DIGITALIZE_CLIENT_SIGNATURE) ?
                CollectionFlowStatus.PENDING_BUSINESS_USER_ACTION :
                CollectionFlowStatus.PENDING_CLIENT_RESPONSE;
    }

    public void handleExtensionFlowLog (CollectionFlowLog flowLog) {

        if (!QueryService.existsEntity(FlowProcessorEntity.class,
                "e.id = :p0 and e.entityType = :p1 and e.stopped = false and e.completed = false",
                new Object[]{flowLog.getRelatedToId(), flowLog.getRelatedToEntity()})) {
            flowLog.setEnded(true);
            collectionFlowLogRepository.save(flowLog);
        }
    }

    //----------------------------------------------------------------------------//
    //---------------------------- HELPER FUNCTIONS ------------------------------//
    //----------------------------------------------------------------------------//

    public static CollectionFlowStatus getCollectionFlowStatusFromRejectionToDoType (DirectDebitRejectionToDoType rejectionToDoType) {
        if (rejectionToDoType == null)
            return null;
        switch (rejectionToDoType) {
            case WAITING_BANK_RESPONSE:
            case WAITING_BANK_RESPONSE_B:
            case WAITING_BANK_RESPONSE_B_CASE_D:
            case WAITING_BANK_RESPONSE_B_BOUNCED:
                return CollectionFlowStatus.PENDING_BANK_RESPONSE;
            case WAITING_RE_SCHEDULE_B:
            case WAITING_ACCOUNTANT_ACTION:
            case WAITING_FLOW_PAUSE_B_BOUNCED:
            case WAITING_ACCOUNTANT_ACTION_B_CASE_D:
            case WAITING_ACCOUNTANT_ACTION_B_BOUNCED:
                return CollectionFlowStatus.PENDING_BUSINESS_USER_ACTION;
            case WAITING_CLIENT_SIGNATURE:
            case WAITING_CLIENT_SIGNATURE_B_CASE_D:
            case WAITING_CLIENT_SIGNATURE_B_BOUNCED:
            case WAITING_CLIENT_INFO_DUE_AUTHORIZATION:
            case WAITING_CLIENT_INFO_DUE_AUTHORIZATION_B_CASE_D:
                return CollectionFlowStatus.PENDING_CLIENT_RESPONSE;
        }
        return null;
    }

    public static String getCollectionFlowNotesFromRejectionCategory (DirectDebitRejectCategory rejectCategory) {
        if (rejectCategory != null)
            switch (rejectCategory) {
                case Signature:
                    return CollectionFlowLogController.SIGNATURE_REJECTION_REASON_NOTES;
                case Authorization:
                    return CollectionFlowLogController.AUTHORIZATION_REJECTION_REASON_NOTES;
                case Invalid_Account:
                    return CollectionFlowLogController.INVALID_ACCOUNT_REJECTION_REASON_NOTES;
                case Account:
                    return CollectionFlowLogController.ACCOUNT_REJECTION_REASON_NOTES;
                case EID:
                    return CollectionFlowLogController.EID_REJECTION_REASON_NOTES;
            }
        return "";
    }

    public CollectionFlowJobHistory getNewCollectionFlowHistory () {
        Date calculateFrom;
        Date calculateTo;
        Date now = new Date();
        CollectionFlowJobHistory lastFlowJobHistory = getLastCollectionFlowHistoryByStatus(CollectionJobStatus.SUCCEED);
        if (lastFlowJobHistory != null) {
            calculateFrom = lastFlowJobHistory.getCalculateTo();
            calculateTo = now;
        } else {
            calculateFrom = DateUtil.getFirstDateEver();
            calculateTo = now;
        }

        CollectionFlowJobHistory collectionFlowJobHistory = new CollectionFlowJobHistory(calculateFrom, calculateTo, CollectionJobStatus.RUNNING);
        return collectionFlowJobHistoryRepository.save(collectionFlowJobHistory);
    }

    public CollectionFlowJobHistory getLastCollectionFlowHistoryByStatus (CollectionJobStatus status) {
        return collectionFlowJobHistoryRepository.findTopByStatusOrderByIdDesc(status);
    }

    public Map<String, Object> getSwitchBankInfoDetails (ContractPaymentTerm newCPT) {
        Map<String, Object> returnResult = new HashMap<>();
        Boolean endedFlag = false;
        CollectionFlowStatus flowStatus = null;
        Date idleSince = null;
        List<DirectDebitStatus> validStatuses = Arrays.asList(
                DirectDebitStatus.PENDING, DirectDebitStatus.PENDING_DATA_ENTRY, DirectDebitStatus.CONFIRMED, DirectDebitStatus.IN_COMPLETE);

        if (!newCPT.isActive())
            endedFlag = true;
        else {
            List<ContractPaymentTerm> oldCPTs = contractPaymentTermRepository.findOldCptAfterSwitching(newCPT.getContract());
            ContractPaymentTerm oldCPT = !oldCPTs.isEmpty() ? oldCPTs.get(0) : null;
            if (oldCPT != null) {
                //search for old DDs if found then we didn't cancel the old dds yet and the flow is still running
                List<DirectDebitStatus> invalidStatuses = Arrays.asList(
                        DirectDebitStatus.CANCELED, DirectDebitStatus.PENDING_FOR_CANCELLATION);
                
                List<DirectDebit> oldDDs = directDebitRepository.findByContractPaymentTermAndStatusIn(
                        oldCPT, validStatuses, invalidStatuses).stream()
                        .filter(dd -> (dd.getType().equals(DirectDebitType.ONE_TIME) && validStatuses.contains(dd.getMStatus())) || !dd.getType().equals(DirectDebitType.ONE_TIME))
                        .filter(dd -> !DDUtils.isDDPendingSent(dd)).collect(Collectors.toList());

                if (!oldDDs.isEmpty()) {
                    // now we need to check the new DDs created on the new CPT to define their status right now.
                    List<DirectDebit> newDDs = directDebitRepository.findByContractPaymentTerm(newCPT);
                    
                    OUTER:
                    for (DirectDebit directDebit : newDDs) {
                        switch (directDebit.getStatus()) {
                            case IN_COMPLETE:
                                if (graphicDesignerTodoRepository.existsByContractIdAndToDoTypeAndCompletedFalse(newCPT.getContract().getId(), GraphicToDoType.ACCOUNTING_DIGITALIZE_CLIENT_SIGNATURE))
                                    flowStatus = CollectionFlowStatus.PENDING_BUSINESS_USER_ACTION;
                                else
                                    flowStatus = CollectionFlowStatus.PENDING_CLIENT_RESPONSE;
                                idleSince = directDebit.getLastModificationDate();
                                break OUTER;
                            case PENDING_DATA_ENTRY:
                                flowStatus = CollectionFlowStatus.PENDING_BUSINESS_USER_ACTION;
                                idleSince = directDebit.getLastModificationDate();
                                break OUTER;
                            case PENDING:
                                if (directDebit.getDirectDebitFiles().stream()
                                        .anyMatch(directDebitFile -> directDebitFile.getStatus().equals(DirectDebitFileStatus.SENT)))
                                    
                                    flowStatus = CollectionFlowStatus.PENDING_BANK_RESPONSE;
                                else
                                    flowStatus = CollectionFlowStatus.PENDING_BUSINESS_USER_ACTION;
                                idleSince = directDebit.getLastModificationDate();
                                break OUTER;
                            default:
                                break;
                        }
                    }

                    if (flowStatus == null) {
                        flowStatus = CollectionFlowStatus.PENDING_BANK_RESPONSE;
                        idleSince = newCPT.getCreationDate();
                    }
                } else endedFlag = true;
            } else {
                endedFlag = true;
            }
        }

        returnResult.put("endedFlag", endedFlag);
        returnResult.put("flowStatus", flowStatus);
        returnResult.put("idleSince", idleSince);

        return returnResult;
    }


    public List<Map<String, Object>> getRunningFlowsMessagesByContractACC8311(Contract contract) {
        List<Map<String, Object>> l = new ArrayList<>();
        ContractPaymentTerm cpt = contract.getActiveContractPaymentTerm();

        addRunningFlowsMessagesByContractACC8311_BouncedFlow(contract, l);

        addRunningFlowsMessagesByContractACC8311_IncompleteDDs(cpt, l);

        addRunningFlowsMessagesByContractACC8311_RejectedDDs(cpt, l);

        addRunningFlowsMessagesByContractACC8311_Ipam(contract, l);

        addRunningFlowsMessagesByContractACC8311_PayingViaCc(cpt, l);

        return l;
    }

    private void addRunningFlowsMessagesByContractACC8311_BouncedFlow(Contract c, List<Map<String, Object>> l) {
        Payment p = paymentRepository.findFirstByContractAndStatusAndReplacedFalseOrderByDateOfPaymentAsc(
                c, PaymentStatus.BOUNCED);
        if (p == null) return;
        ContractPaymentConfirmationToDo todo = Setup.getApplicationContext()
                .getBean(PaymentService.class)
                .createToDoIfNotExists(
                        p, p.getContract().getActiveContractPaymentTerm(), ContractPaymentConfirmationToDo.Source.BOUNCED_PAYMENT_FLOW);

        if (todo == null) return;

        Template t = TemplateUtil.getTemplate(CmOngoingCollectionFlowsTemplateCode.CM_ONGOING_COLLECTION_FLOWS_BOUNCED_PAYMENT.toString());
        if (t == null) return;
        String pay_link = Setup.getApplicationContext()
                .getBean(ContractPaymentConfirmationToDoService.class)
                .getPayingViaCreditCardLink(todo);

        String text = TemplateUtil.compileTemplate(t,
                c, new HashMap<String, String>() {{
                    put("link_to_pay_via_cc", pay_link);
                }});

        l.add(new HashMap<String, Object>() {{
            put("flowName", "Bouncing Flow");
            put("message", text);
            put("ccLink", pay_link == null  || pay_link.isEmpty() ? "missing" : pay_link);
            put("signLink", "missing");

        }});
    }

    private void addRunningFlowsMessagesByContractACC8311_IncompleteDDs(
            ContractPaymentTerm cpt, List<Map<String, Object>> l) {

        if (!Setup.getRepository(DirectDebitRepository.class)
                .existsIncompleteDDsByCpt(cpt)) return;

        List<String> missingBankInfo = Setup.getApplicationContext()
                .getBean(DirectDebitService.class)
                .getMissingBankInfo(cpt);

        if (missingBankInfo.isEmpty()) return;

        boolean missingBankInfoRunning = flowProcessorService.existsRunningFlow(
                cpt.getContract(), FlowEventConfig.FlowEventName.INCOMPLETE_FLOW_MISSING_BANK_INFO);
        boolean rejectDataEntryRunning = cpt.getIsIBANRejected() ||
                cpt.getIsEidRejected() ||
                cpt.getIsAccountHolderRejected();

        if (!missingBankInfoRunning && !rejectDataEntryRunning) return;

        StringBuilder sb = new StringBuilder();


        boolean signatureIsMissing = false;
        if (missingBankInfo.contains("Signatures")) {
            signatureIsMissing = true;
            sb.append("signatures");
            sb.append(missingBankInfo.size() > 1 ? " and " : "");
            missingBankInfo.remove("Signatures");
        }

        if (!missingBankInfo.isEmpty()) {
            sb.append(missingBankInfo.size() == 1 ? "document of " : "documents of ");
            sb.append(String.join(" - ", missingBankInfo));
        }

        String signLink = Setup.getApplicationContext()
                .getBean(CCAppContentService.class)
                .getSignDdWebLinkForPaymentSection(cpt, null,
                        true,
                        missingBankInfoRunning || signatureIsMissing);

        Template t = TemplateUtil.getTemplate(CmOngoingCollectionFlowsTemplateCode.CM_ONGOING_COLLECTION_FLOWS_INCOMPLETE_DDS.toString());
        if (t == null) return;

        String text = TemplateUtil.compileTemplate(t,
                cpt.getContract(), new HashMap<String, String>() {{
                    put("missing_info", sb.toString());
                    put("link_to_upload_documents", signLink);
                }});

        l.add(new HashMap<String, Object>() {{
            put("flowName", "Incomplete DDs");
            put("message", text);
            put("ccLink", "missing");
            put("signLink", "missing");
        }});
    }

    private void addRunningFlowsMessagesByContractACC8311_RejectedDDs(ContractPaymentTerm cpt, List<Map<String, Object>> l) {

        Template t = TemplateUtil.getTemplate(CmOngoingCollectionFlowsTemplateCode.CM_ONGOING_COLLECTION_FLOWS_REJECTED_DDS.toString());

        if (t == null) return;

        List<DirectDebit> rejectedDds = directDebitRepository.findDirectDebitByRejectionFlowByCpt(cpt);
        Map<String,Object> m = ccAppService.getRejectionFLowSectionInfo(rejectedDds, cpt.getContract());
        if (m == null || !m.containsKey("rejectedDd")) return;

        DirectDebit d = (DirectDebit) m.get("rejectedDd");

        DirectDebitRejectCategory category = d.getDirectDebitRejectionToDo() != null ?
                d.getDirectDebitRejectionToDo().getLastRejectCategory() :
                d.getDirectDebitBouncingRejectionToDo().getLastRejectCategory();

        String linkToUploadDocument = Setup.getApplicationContext()
                .getBean(CCAppContentService.class)
                .getSignDdWebLinkForPaymentSection(cpt, category, false, false);

        String text = TemplateUtil.compileTemplate(t,
                cpt.getContract(), new HashMap<String, String>() {{
                    put("reason_of_rejection", category != null ? category.getLabel() : "");
                    put("link_to_upload_documents", linkToUploadDocument);
                }});

        l.add(new HashMap<String, Object>() {{
            put("flowName", "Rejected DDs");
            put("message", text);
            put("ccLink", "missing");
            put("signLink", "missing");
        }});
    }

    private void addRunningFlowsMessagesByContractACC8311_Ipam(Contract contract, List<Map<String, Object>> l) {
        if (!Setup.getApplicationContext()
                .getBean(FlowProcessorService.class)
                .existsRunningFlow(contract, FlowEventConfig.FlowEventName.CLIENT_PAID_CASH_NO_SIGNATURE_PROVIDED))
            return;

        Template t = TemplateUtil.getTemplate(CmOngoingCollectionFlowsTemplateCode.CM_ONGOING_COLLECTION_FLOWS_IPAM.toString());
        if (t == null) return;

        Map<String, Object> signDDMap = new HashMap<>();
        signDDMap.put("cpt", contract.getActiveContractPaymentTerm());
        signDDMap.put("additionalInfo", new HashMap<String, Object>() {{
            put("sentFrom", "GPT_addRunningFlowsMessagesByContractACC8311_Ipam");
        }});

        ContractPaymentConfirmationToDo todo = Setup.getApplicationContext()
                .getBean(AfterCashFlowService.class).getPaymentTodo(contract.getActiveContractPaymentTerm());

        String ccLink = todo == null ? "" :
                Setup.getApplicationContext()
                .getBean(ContractPaymentConfirmationToDoService.class)
                .getPayingViaCreditCardLink(todo);

        String signingLink =  Setup.getApplicationContext()
                .getBean(Utils.class)
                .getSingDDLink(signDDMap);

        String text = TemplateUtil.compileTemplate(t,
                contract, new HashMap<String, String>() {{
                    put("signing_link", signingLink);
                }});

        l.add(new HashMap<String, Object>() {{
            put("flowName", "IPAM");
            put("message", text);
            put("ccLink", ccLink == null  || ccLink.isEmpty() ? "missing" : ccLink);
            put("signLink", signingLink == null  || signingLink.isEmpty() ? "missing" : signingLink);
        }});
    }

    private void addRunningFlowsMessagesByContractACC8311_PayingViaCc(ContractPaymentTerm cpt, List<Map<String, Object>> l) {

        if (!cpt.getContract().isPayingViaCreditCard()) return;

        boolean isEligibleForTokenizationViaContract = ContractService.isEligibleForTokenizationViaContract(cpt.getContract());
        ContractPaymentConfirmationToDo toDo = ccAppContentService.getToDoForPayingViaCCOrOMA(cpt, isEligibleForTokenizationViaContract);

        CmOngoingCollectionFlowsTemplateCode templateName =
                toDo == null && isEligibleForTokenizationViaContract ?
                        CmOngoingCollectionFlowsTemplateCode.CM_ONGOING_COLLECTION_FLOWS_CLIENT_HAS_TOKENIZED_CARD_PAYING_VIA_CC :
                        CmOngoingCollectionFlowsTemplateCode.CM_ONGOING_COLLECTION_FLOWS_CLIENT_PAYING_VIA_CC;
        if (toDo == null && templateName.equals(CmOngoingCollectionFlowsTemplateCode.CM_ONGOING_COLLECTION_FLOWS_CLIENT_PAYING_VIA_CC)) return;

        Template t = TemplateUtil.getTemplate(templateName.toString());
        if (t == null) return;

        Map<String, Object> signDDMap = new HashMap<>();
        signDDMap.put("cpt", cpt);
        signDDMap.put("additionalInfo", new HashMap<String, Object>() {{
            put("sentFrom", "GPT_addRunningFlowsMessagesByContractACC8311_PayingViaCc");
        }});

        String pay_link = toDo == null ? "" :
                Setup.getApplicationContext()
                .getBean(ContractPaymentConfirmationToDoService.class)
                .getPayingViaCreditCardLink(toDo);

        String signingLink = Setup.getApplicationContext()
                .getBean(Utils.class)
                .getSingDDLink(signDDMap);
        String text = TemplateUtil.compileTemplate(
                t,
                cpt.getContract(),
                new HashMap<String, String>() {{
                        if (toDo != null) {
                            put("paying_via_cc_link", pay_link);
                        }
                        put("signing_link", signingLink);
                    }});

        l.add(new HashMap<String, Object>() {{
            put("flowName", "Paying via CC");
            put("message", text);
            put("ccLink", pay_link == null  || pay_link.isEmpty() ? "missing" : pay_link);
            put("signLink","missing");
        }});
    }
}