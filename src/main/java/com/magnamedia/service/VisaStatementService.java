package com.magnamedia.service;

import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.Attachment;
import com.magnamedia.core.helper.Storage;
import com.magnamedia.entity.AmwalStatementRecord;
import com.magnamedia.entity.NoqodiStatementRecord;
import com.magnamedia.entity.VisaStatement;
import com.magnamedia.entity.VisaStatementTransaction;
import com.magnamedia.extra.NotificationUtils;
import com.magnamedia.extra.VisaStatementStatus;
import com.magnamedia.extra.VisaStatementTransactionType;
import com.magnamedia.extra.VisaStatementType;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.repository.*;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.DataFormatter;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.NumberFormat;
import java.util.*;
import java.util.logging.Logger;

@Service
public class VisaStatementService {
    protected static final Logger logger = Logger.getLogger(VisaStatementService.class.getName());

    @Autowired
    private VisaStatementRepository visaStatementRepository;
    @Autowired
    private NewVisaRequestExpenseRepository newVisaRequestExpenseRepository;
    @Autowired
    private VisaStatementTransactionRepository visaStatementTransactionRepository;
    @Autowired
    private NoqodiStatementRecordRepository noqodiStatementRecordRepository;

    @Transactional
    public void createAllVisaTransactions(Map<String, Object> payload) {
        VisaStatement statement = visaStatementRepository.findOne(Long.parseLong(payload.get("entityId").toString()));
        if (statement == null) return;

        if (VisaStatementType.Amwal.equals(statement.getType())) {
            extractAmwalRecordsAndMappingThem(statement);
        } else if (VisaStatementType.Noqodi.equals(statement.getType())) {
            extractNoqodiRecordsAndMappingThem(statement);
        }

        statement.setStatus(VisaStatementStatus.PENDING);
        visaStatementRepository.save(statement);
    }


    public Boolean extractNoqodiRecordsAndMappingThem(VisaStatement visaStatement) {
        Attachment attachment = null;
        if (visaStatement != null) {
            if (visaStatement.getAttachmentsCount() > 0) {
                attachment = visaStatement.getAttachments().get(0);
            }
        }
        if (attachment == null) return false;

        try {
            DataFormatter formatter = new DataFormatter();
            Workbook workbook = null;
            try {
                workbook = new XSSFWorkbook(Storage.getStream(attachment));
            } catch (Exception e) {
                try {
                    workbook = new HSSFWorkbook(Storage.getStream(attachment));
                } catch (Exception e1) {
                    logger.info("Error while parsing file " + attachment.getName() + " for statement #" + visaStatement.getId()+ NotificationUtils.getExceptionStackTrace(e));
                    throw new RuntimeException("Error while parsing file " + attachment.getName() +NotificationUtils.getExceptionStackTrace(e));
                }
            }

            List<NoqodiStatementRecord> records = new ArrayList<>();
            if (workbook != null) {
                NumberFormat nf_in = NumberFormat.getNumberInstance(Locale.ENGLISH);
                Sheet sheet = workbook.getSheetAt(0);
                Iterator<Row> rowIterator = sheet.iterator();

                while (rowIterator.hasNext()) {
                    Row row = rowIterator.next();
                    String srNumber = formatter.formatCellValue(row.getCell(0)).trim();

                    if (!StringUtils.isNumeric(srNumber)) continue;

                    String transactionDateString = formatter.formatCellValue(row.getCell(1)).trim();
                    Date transactionDate = DateUtil.parseNoqodiDate(transactionDateString);
                    String transactionNumber = formatter.formatCellValue(row.getCell(2)).trim();
                    String description = formatter.formatCellValue(row.getCell(3)).trim();
                    Double debit = !formatter.formatCellValue(row.getCell(4)).trim().isEmpty() ?
                            nf_in.parse(formatter.formatCellValue(row.getCell(4)).trim()).doubleValue() : 0.0;
                    Double credit = !formatter.formatCellValue(row.getCell(5)).trim().isEmpty() ?
                            nf_in.parse(formatter.formatCellValue(row.getCell(5)).trim()).doubleValue() : 0.0;
                    Double balance = !formatter.formatCellValue(row.getCell(6)).trim().isEmpty() ?
                            nf_in.parse(formatter.formatCellValue(row.getCell(6)).trim()).doubleValue() : 0.0;

                    NoqodiStatementRecord record = new NoqodiStatementRecord();
                    record.setSrNumber(srNumber);
                    record.setTransactionDate(transactionDate);
                    record.setTransactionNumber(transactionNumber);
                    record.setDescription(description);
                    record.setDebit(debit);
                    record.setCredit(credit);
                    record.setBalance(balance);
                    record.setStatement(visaStatement);

                    records.add(record);
                }

                records = noqodiStatementRecordRepository.saveAll(records);
                // create Master Recorder
                for (NoqodiStatementRecord n1 : records) {
                    List<NoqodiStatementRecord> brothers = noqodiStatementRecordRepository.findByStatementAndTransactionNumberAndIsFinal(visaStatement, n1.getTransactionNumber(), false);
                    if (brothers != null && brothers.size() > 1) {
                        createMasterRecordNoqodi(brothers);
                    }
                }

                records = noqodiStatementRecordRepository.findByStatementAndIsFinal(visaStatement, false);
                // matching with visa expanse
                for (NoqodiStatementRecord record : records) {
                    VisaStatementTransaction t = new VisaStatementTransaction();

                    if (record.getCredit() > 0) {
                        t.setStatement(visaStatement);
                        t.setAmount(record.getCredit());
                        t.setCredit(true);
                        t.setReferenceNumber(record.getTransactionNumber());
                        t.setRowRecordDate(record.getTransactionDate());
                        if (record.getAmounts() != null && !record.getAmounts().isEmpty()) {
                            t.setAmounts(record.getAmounts());
                        }

                        if (record.getDescription() != null && record.getDescription().toLowerCase()
                                .contains("ADCB Purchase Credit".toLowerCase())) {

                            t.setType(VisaStatementTransactionType.Ignore);
                            t.setFinished(true);
                        } else {
                            t.setType(VisaStatementTransactionType.MissingFromERP);
                        }

                    } else if (record.getDebit() > 0) {
                        List<Object[]> visaExpenses = newVisaRequestExpenseRepository
                                .findByExpenseReferenceNumberAndCreationDate(record.getTransactionNumber(),
                                        visaStatement.getStart(), visaStatement.getEnd());

                        t.setRowRecordDate(record.getTransactionDate());
                        t.setReferenceNumber(record.getTransactionNumber());
                        t.setAmount(record.getDebit());
                        t.setStatement(visaStatement);

                        if (record.getAmounts() != null && !record.getAmounts().isEmpty()) {
                            t.setAmounts(record.getAmounts());
                        }

                        if (visaExpenses.isEmpty()) {
                            t.setType(VisaStatementTransactionType.MissingFromERP);
                        } else {
                            t = new VisaStatementTransaction(visaExpenses.get(0), visaStatement, record.getDebit());
                            t.setType(record.getDebit() != null && record.getDebit().equals(getErpFullAmount(visaExpenses.get(0))) ?
                                    VisaStatementTransactionType.Matched :
                                    VisaStatementTransactionType.SameReferenceNumberButDifferentAmount);
                        }
                    }

                    t = visaStatementTransactionRepository.save(t);
                    record.setTransaction(t);

                    noqodiStatementRecordRepository.save(record);
                }
            }
        } catch (Exception e) {
            logger.info("Error while parsing file " + attachment.getName() + " for visaStatement #" + visaStatement.getId()+ NotificationUtils.getExceptionStackTrace(e));
            throw new RuntimeException("Error while parsing file " + attachment.getName()+NotificationUtils.getExceptionStackTrace(e));
        }

        return true;
    }

    public Boolean extractAmwalRecordsAndMappingThem(VisaStatement visaStatement){
        Attachment attachment = null ;
        if (visaStatement != null){
            if (visaStatement.getAttachmentsCount() > 0){
                attachment = visaStatement.getAttachments().get(0);
            }
        }

        if (attachment == null) return false;

        try {
            DataFormatter formatter = new DataFormatter();
            Workbook workbook = null;
            try {
                workbook = new XSSFWorkbook(Storage.getStream(attachment));
            }
            catch (Exception e){
                try {
                    workbook = new HSSFWorkbook(Storage.getStream(attachment));
                }
                catch (Exception e1){
                    logger.info("Error while parsing file "+ attachment.getName() + " for statement #" + visaStatement.getId()+ NotificationUtils.getExceptionStackTrace(e));
                    throw new RuntimeException("Error while parsing file "+ attachment.getName()+NotificationUtils.getExceptionStackTrace(e));
                }
            }

            NumberFormat nf_in = NumberFormat.getNumberInstance(Locale.ENGLISH);
            Sheet sheet = workbook.getSheetAt(0);
            Iterator<Row> rowIterator = sheet.iterator();
            List<AmwalStatementRecord> records = new ArrayList<>();
            if (rowIterator.hasNext()) rowIterator.next();

            while (rowIterator.hasNext()) {
                Row row = rowIterator.next();
                String transactionDateString = formatter.formatCellValue(row.getCell(0)).trim();
                if (transactionDateString == null || transactionDateString.isEmpty()) break;

                Date transactionDate =DateUtil.parseAmwalDate(transactionDateString);
                String transactionType = formatter.formatCellValue(row.getCell(1)).trim();
                String transactionNumber = formatter.formatCellValue(row.getCell(2)).trim();
                String transactionStatue = formatter.formatCellValue(row.getCell(3)).trim();
                Double oldBalance = !formatter.formatCellValue(row.getCell(4)).trim().isEmpty() ?
                        nf_in.parse(formatter.formatCellValue(row.getCell(4)).trim()).doubleValue() : 0.0;
                Double availableBalance = !formatter.formatCellValue(row.getCell(5)).trim().isEmpty() ?
                        nf_in.parse(formatter.formatCellValue(row.getCell(5)).trim()).doubleValue() : 0.0;
                Double amount = !formatter.formatCellValue(row.getCell(6)).trim().isEmpty() ?
                        nf_in.parse(formatter.formatCellValue(row.getCell(6)).trim()).doubleValue() : 0.0;

                AmwalStatementRecord record = new AmwalStatementRecord();
                record.setStatement(visaStatement);
                record.setTransactionDate(transactionDate);
                record.setTransactionType(transactionType);
                record.setTransactionNumber(transactionNumber);
                record.setTransactionStatue(transactionStatue);
                record.setOldBalance(oldBalance);
                record.setAvailableBalance(availableBalance);
                record.setAmount(amount);

                List<Object[]> visaExpenses = newVisaRequestExpenseRepository.findByExpenseReferenceNumberAndCreationDate(
                        transactionNumber, visaStatement.getStart(), visaStatement.getEnd());

                VisaStatementTransaction t = new VisaStatementTransaction();
                t.setStatement(visaStatement);
                t.setAmount(amount);
                t.setReferenceNumber(transactionNumber);
                t.setRowRecordDate(transactionDate);

                if (visaExpenses.isEmpty()){
                    t.setType(VisaStatementTransactionType.MissingFromERP);
                }else {
                    t = new VisaStatementTransaction(visaExpenses.get(0), visaStatement, amount);
                    t.setType(record.getAmount() != null && record.getAmount().equals(getErpFullAmount(visaExpenses.get(0))) ?
                            VisaStatementTransactionType.Matched :
                            VisaStatementTransactionType.SameReferenceNumberButDifferentAmount);
                }

                t = visaStatementTransactionRepository.save(t);
                record.setTransaction(t);

                records.add(record);
            }

            if (!records.isEmpty()) Setup.getRepository(AmwalStatementRecordRepository.class).saveAll(records);
        } catch (Exception ex) {
            logger.info("Error while parsing file "+ attachment.getName() + " for visaStatement #" + visaStatement.getId()+ NotificationUtils.getExceptionStackTrace(ex));
            throw new RuntimeException("Error while parsing file "+ attachment.getName()+ NotificationUtils.getExceptionStackTrace(ex));
        }

        return true;
    }

    public void createMasterRecordNoqodi(List<NoqodiStatementRecord> records) {
        double amount = 0;
        double amountCredit = 0;
        StringBuilder amounts = new StringBuilder();
        StringBuilder amountsCredit = new StringBuilder();
        NoqodiStatementRecord master = new NoqodiStatementRecord();

        for (NoqodiStatementRecord statementRecord : records) {
            amount += statementRecord.getDebit();
            amountCredit+= statementRecord.getCredit();
            amounts.append(statementRecord.getDebit()).append(";");
            amountsCredit.append(statementRecord.getCredit()).append(";");
            statementRecord.setMaster(master);
            statementRecord.setFinal(true);
        }

        master.setTransactionNumber(records.get(0).getTransactionNumber());
        master.setTransactionDate(records.get(0).getTransactionDate());
        master.setDebit(amount);
        master.setCredit(amountCredit);
        master.setAmounts(amount >0  ? amounts.toString() : amountsCredit.toString());
        master.setStatement(records.get(0).getStatement());

        noqodiStatementRecordRepository.save(master);
        noqodiStatementRecordRepository.saveAll(records);
    }

    public double getErpFullAmount(Object[] visaExpense) {

        return (visaExpense[2] == null ? 0 : (Double) visaExpense[2]) +
                (visaExpense[3] == null ? 0 : (Double) visaExpense[3]) +
                (visaExpense[4] == null ? 0 : (Double) visaExpense[4]);
    }
}