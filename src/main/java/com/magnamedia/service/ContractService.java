package com.magnamedia.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.magnamedia.controller.ContractPaymentTermController;
import com.magnamedia.core.Setup;
import com.magnamedia.core.configuration.ObjectMapperConfiguration;
import com.magnamedia.core.entity.BackgroundTask;
import com.magnamedia.core.entity.BaseAdditionalInfo;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.helper.BackgroundTaskService;
import com.magnamedia.core.helper.CurrentRequest;
import com.magnamedia.core.helper.HistorySelectQuery;
import com.magnamedia.core.imc.InterModuleConnector;
import com.magnamedia.core.repository.PicklistItemRepository;
import com.magnamedia.core.repository.PicklistRepository;
import com.magnamedia.core.type.BackgroundTaskQueues;
import com.magnamedia.entity.*;
import com.magnamedia.entity.workflow.DirectDebitRejectionToDo;
import com.magnamedia.extra.*;
import com.magnamedia.extra.annotations.UsedBy;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.helper.PicklistHelper;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.*;
import com.magnamedia.repository.*;
import com.magnamedia.workflow.type.ClientRefundTodoType;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;
import org.joda.time.format.DateTimeFormat;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.magnamedia.repository.ContractPaymentTypeRepository;

import java.io.InputStream;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 *
 * <AUTHOR> Hachem
 */

@Service
public class ContractService {

    private final static Logger logger = Logger.getLogger(ContractService.class.getName());

    @Autowired
    private InterModuleConnector moduleConnector;
    @Autowired
    private PaymentRepository paymentRepository;
    @Autowired
    private FlowProcessorService flowProcessorService;
    @Autowired
    private ContractPaymentTermServiceNew contractPaymentTermServiceNew;
    @Autowired
    private DirectDebitSignatureService directDebitSignatureService;
    @Autowired
    private AccountingEntityPropertyRepository accountingEntityPropertyRepository;
    @Autowired
    private ContractRepository contractRepository;
    @Autowired
    private ContractPaymentConfirmationToDoRepository contractPaymentConfirmationToDoRepository;
    @Autowired
    private CalculateDiscountsWithVatService calculateDiscountsWithVatService;

    // ACC-8069
    @UsedBy(modules = UsedBy.Modules.Staff_Management)
    public Boolean isClientInTrialPeriod(Long contractId) {

        return accountingEntityPropertyRepository.existsByOriginAndKeys(contractRepository.findOne(contractId),
                Arrays.asList(
                        Contract.DOWNGRADING_NATIONALITY_JOB_PASSED_WHILE_HAS_NO_MAID,
                        Contract.DOWNGRADING_NATIONALITY_DATE,
                        Contract.UPGRADING_NATIONALITY_DATE));
    }

    public Double getCorrectedBalance(Long contractId, String dateToBeSent) {
        String apiUrl = "/clientmgmt/contractTermination/recalculateV2/"
                + contractId
                + "?scheduledDateOfTermination="
                + dateToBeSent;

        logger.log(Level.SEVERE, "url: " + apiUrl);
        ContractTerminationHelper response = moduleConnector.postJson(apiUrl, new ArrayList(), ContractTerminationHelper.class);
        logger.log(Level.SEVERE, "client balance: " + response.getCancellationLog().getCorrectedBalance());

        return response.getCancellationLog().getCorrectedBalance();
    }

    public void updateContractFromClientMgt(Map<String, Object> body) {
        try {
            ObjectMapper objectMapper = Setup.getApplicationContext()
                    .getBean(ObjectMapperConfiguration.class)
                    .objectMapper();

            moduleConnector.call(
                    "clientmgmt", "contractController", "updateContract",
                    Map.class, new Class[] { String.class },
                    new Object[] { objectMapper.writeValueAsString(body) });
        } catch(Exception ex) {
            logger.log(Level.SEVERE, "error while updating Contract in 'updateContractFromClientMgt'", ex);
            throw new RuntimeException("error while updating Contract in 'updateContractFromClientMgt' -> " + ex.getMessage());
        }
    }

    public void updateContractFromClientMgtAsync(Map<String, Object> body) {
        logger.info("contract map keys: " + body.keySet());
        logger.info("contract map values: " + body.values());

        moduleConnector.postJsonAsync("clientmgmt/contract/update", body);
        logger.info("IMC added");
    }

    public void retractContractTermination(
        Contract contract) {

        logger.log(Level.SEVERE, "contract retract termination requested from CM");
        moduleConnector.getAsync(
            "/clientmgmt/contractTermination/undoContractTermination?contractId=" + contract.getId(),
            Object.class);
    }

    public void markContractSettled(Long contractId) {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("id", contractId);
        parameters.put("settled", true);

        try {
            moduleConnector.postJsonAsync("clientmgmt/contract/update", parameters);
        } catch (Exception ex) {
            logger.log(Level.SEVERE, null, ex);
            throw new RuntimeException("Inter Module Connecter Problem.");
        }

        logger.log(Level.SEVERE, "contract set as settled: " + contractId);
    }

    // ACC-5629
    public Housemaid getLastHousemaid(Contract contract) {

        return contract.getHousemaid() != null ?
                contract.getHousemaid() : contract.getActiveContractPaymentTerm().getHousemaid();
    }

    public InputStream getTaxInvoice(Contract contract, boolean contractExtend, int extendDuration, boolean isCash) {
        InputStream in = null;
        try {

            if (contract == null) throw new RuntimeException("Contract not found");

            ContractPaymentTerm cpt = contract.getActiveContractPaymentTerm();

            Map m = Setup.getApplicationContext()
                    .getBean(ContractPaymentTermServiceNew.class).getContractInfo(
                            cpt, contractExtend, extendDuration, isCash);
            List<DirectDebit> directDebits = (List) m.get("toBeSignedDirectDebits");

            ContractPayment cp = null;
            if (directDebits != null) {
                for (DirectDebit dd : directDebits) {
                    cp = dd.getPayments().stream()
                            .filter(p -> p.getPaymentType().getCode()
                                    .equals(AbstractPaymentTypeConfig.MONTHLY_PAYMENT_TYPE_CODE))
                            .findFirst().orElse(null);
                }
            }

            if (cp == null) cp = (ContractPayment) m.get("nextPayment");
            if (cp == null) throw new RuntimeException("No Payment Generated");

            StringBuilder log = calculateDiscountsWithVatService.updateVatFields(cp);
            logger.info(log.toString());

            in = PaymentHelper.generateTaxInvoice(contract, cp);

        } catch (Exception e) {
            e.printStackTrace();
        }

        return in;
    }

    public Map<String, Object> getContractDetailsCM3720(Contract c, String sectionName) {
        switch (sectionName.toUpperCase()) {
            case "SEC_3_1":
                return getContractDetailsCM3720Sec_3_1(c);
            case "SEC_3_DATA_ENTRY":
                return getContractDetailsCM3720Sec_3_DataEntry(c);
            case "SEC_DD_INFO":
                return getContractDetailsCM3720Sec_DdInfo(c);
        }

        return new HashMap<>();
    }

    private Map<String, Object> getContractDetailsCM3720Sec_3_1(Contract c) {
        ContractPaymentTerm cpt = c.getActiveContractPaymentTerm();
        Map<String, Object> r = new HashMap<>();
        // collection flows
        List<String> flows = Setup.getRepository(CollectionFlowLogRepository.class).findByContractAndEndedFalse(c);
        r.put("ongoingCollectionFlow", flows.isEmpty() ? "" : (flows.size() +
                " Running Collection Flows (" + String.join(", ", flows) + ")"));

        // payment plans
        r.put("paymentPlan", PaymentReceiptHelper.getPaymentsPlanAsInDdcPage(cpt));

        // payment method
        r.put("paymentMethod", Setup.getApplicationContext()
                .getBean(FlowProcessorService.class)
                .isPayingViaCreditCard(c) ? "Credit Card" : "Monthly Bank Payment Form");


        // contract is pre-collected
        List<Map<String, Object>> preCollectedPayments = new ArrayList<>();
        Map<String, Object> preCollectedInfo = new HashMap<>();

        boolean isPreCollected = ContractService.isPreCollectedSalary(c);
        preCollectedInfo.put("isPreCollectedSalary", isPreCollected);

        if (isPreCollected) {
            preCollectedInfo.put("precollectedPaymentDate", new DateTime(calculateDiscountsWithVatService
                    .getDiscountStartDateInMillis(cpt)).toString("yyyy-MM-dd"));
            preCollectedInfo.put("precollectedAmount", c.getWorkerSalaryNew());

            DateTime d = new DateTime(calculateDiscountsWithVatService.getDiscountStartDateInMillis(cpt));

            boolean isDiscountLessThanThreshold = isDiscountEffectiveAfterLessThanThreshold(cpt);

            List<Object[]> currentPreCollectedPayments = paymentRepository.findPreCollectedPaymentsInfo(
                    c,
                    isDiscountLessThanThreshold ? d.dayOfMonth().withMinimumValue().toDate() : null,
                    isDiscountLessThanThreshold ? d.dayOfMonth().withMaximumValue().toDate() : null);

            if (!currentPreCollectedPayments.isEmpty()) {

                preCollectedInfo.put("precollectedPaymentDate", new LocalDate(currentPreCollectedPayments.get(0)[3])
                        .toString("yyyy-MM-dd"));

                preCollectedInfo.put("precollectedAmount", String.format("%,.0f",
                        currentPreCollectedPayments.get(0)[2].equals("monthly_payment") ?
                                currentPreCollectedPayments.get(0)[4] : currentPreCollectedPayments.get(0)[1]));

                for (Object[] row : currentPreCollectedPayments) {

                    preCollectedPayments.add(new HashMap<String, Object>() {{
                        put("status", ((PaymentStatus) row[0]).getLabel());
                        put("amount", "AED " + String.format("%,.0f",
                                row[2].equals("monthly_payment") ? row[4] : row[1]));
                        put("paymentType", row[2]);
                        put("preCollectedPaymentDate", new LocalDate(row[3]).toString("dd MMM yyyy"));
                    }});
                }
            }
            preCollectedInfo.put("currentPreCollectedPayments", preCollectedPayments);
        }

        r.put("preCollectedInfo", preCollectedInfo);

        // current payments
        Map<String, Object> paymentInfo = getContractPaymentInfo(c, true);
        r.put("currentPayments", paymentInfo.get("currentPayments"));
        r.put("currentPayment", paymentInfo.get("currentPayment"));


        // Monthly Bank Payment Forms Status
        r.put("monthlyBankPaymentFormsStatus", "");
        if (r.get("paymentMethod").equals("Monthly Bank Payment Form")) {
            List<Object[]> l = Setup.getRepository(DirectDebitRepository.class)
                    .findDdbStatusAndDdfStatusByCpt(cpt);
            Map<String, Object> m = new HashMap<>();
            m.put("reason", "");
            if (l.isEmpty()) {
                m.put("status", "No DDs yet");
            } else {
                switch ((DirectDebitStatus) l.get(0)[0]) {
                    case IN_COMPLETE:
                        m.put("status", "Incomplete");
                        m.put("reason", String.join(", ", Setup.getApplicationContext()
                                .getBean(DirectDebitService.class)
                                .getMissingBankInfo(cpt)));
                        break;
                    case PENDING:
                        boolean ddfSent = l.stream().anyMatch(o -> DirectDebitFileStatus.SENT.equals(o[2]));
                        m.put("status", ddfSent ? "Pending sent" : "Pending not sent");
                        break;
                    case REJECTED:
                        DirectDebitRejectionToDo toDo = (DirectDebitRejectionToDo) l.get(0)[3];
                        m.put("status", toDo != null &&
                                toDo.getLastDirectDebit().getStatus().equals(DirectDebitStatus.CANCELED) ?
                                DirectDebitStatus.CANCELED.getLabel() :
                                DirectDebitStatus.REJECTED.getLabel());
                        break;
                    default:
                        m.put("status", ((DirectDebitStatus) l.get(0)[0]).getLabel());
                        break;
                }
            }

            r.put("monthlyBankPaymentFormsStatus", m);
        }

        // refund status
        List<Object[]> l = Setup.getRepository(ClientRefundTodoRepository.class)
                .findPendingRefundByContract(c);
        if (l.isEmpty()) {
            r.put("refundStatus", "No Refunds");
        } else {
            List<Map<String, Object>> refunds = new ArrayList<>();
            for(Object[] a : l) {
                refunds.add(new HashMap<String, Object>() {{
                    put("type", (Boolean) a[0] ? "Conditional" : "Unconditional");
                    put("amount", "AED " + String.format("%,.0f", ((Double) a[1])));
                    put("status", ClientRefundTodoType.WAITING_MANAGER_APPROVAL.toString().equals(a[2]) ?
                            "Pending Manager" :
                            ClientRefundTodoType.WAITING_COO_APPROVAL.toString().equals(a[2]) ?
                                    "Pending CEO" : "Pending Bank");
                }});
            }
            r.put("refundStatus", refunds);
        }

        if (isEligibleForTokenizationViaContract(c)) {
            r.put("creditCardInfo", getContractCreditCardInfo(c));
        }

        return r;
    }

    public static boolean isDiscountEffectiveAfterLessThanThreshold(ContractPaymentTerm cpt) {
        logger.info("cpt discoutn effective after -> " + cpt.getDiscountEffectiveAfter());

        return cpt.getDiscountEffectiveAfter() < Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(),
                AccountingModule.PARAMETER_DISCOUNT_EFFECTIVE_AFTER_THRESHOLD));
    }

    private Map<String, Object> getContractDetailsCM3720Sec_3_DataEntry(Contract c) {
        Map<String, Object> r = new HashMap<>();

        // DD data entry
        r.put("ddDataEntry", new HashMap<String, Object>() {{
            put("status", "Not created yet");
            put("date", "");
        }});

        List<Object[]> l = Setup.getRepository(DirectDebitRepository.class)
                .findFirstDataEntryToDo(c.getId());
        if (!l.isEmpty()) {
            r.put("ddDataEntry", new HashMap<String, Object>() {{
                put("status", DirectDebitStatus.PENDING_DATA_ENTRY.toString().equals(l.get(0)[0]) ? "Pending" : "Closed");
                put("date", "(Since " + new LocalDate(l.get(0)[1]).toString("dd MMM yyyy") + ")");
            }});
        }

        return r;
    }

    private Map<String, Object> getContractDetailsCM3720Sec_DdInfo(Contract c) {
        Map<String, Object> r = new HashMap<>();

        r.put("hasConfirmedDd", Setup.getRepository(DirectDebitRepository.class)
                .clientsHasConfirmedDd(c.getClient()));

        return r;
    }

    @Transactional
    public void updatePaperModeAsync(ContractPaymentTerm cpt, boolean signingPaperMode) {
        Contract contract = cpt.getContract();
        if (contract == null ||
                contract.getId() == null ||
                contract.isSigningPaperMode() == signingPaperMode) return;

        List<DirectDebitSignature> signatures = signingPaperMode ?
                directDebitSignatureService.getSignatureSortedList(contract.getClient(), cpt.getEid()):
                directDebitSignatureService.getDisabledSignatureSortedList(contract.getClient(), cpt.getEid());

        signatures.forEach(directDebitSignature -> {
            directDebitSignature.setDisable(signingPaperMode);
            Setup.getRepository(DirectDebitSignatureRepository.class).save(directDebitSignature);
        });

        Map contractMap = new HashMap();
        contractMap.put("id", contract.getId());
        contractMap.put("signingPaperMode", signingPaperMode);

        logger.info("mark the contract#" + contract.getId() + " as signingPaperMode = " + signingPaperMode);
        updateContractAsync(contractMap);
    }

    @Transactional
    public void updateContractAsync(Map contractMap) {
        logger.log(Level.INFO, "update Contract Async");
        if (contractMap == null) return;

        logger.log(Level.INFO, "contract map keys: " + contractMap.keySet());
        logger.log(Level.INFO, "contract map values: " + contractMap.values());

        Setup.getApplicationContext()
                .getBean(InterModuleConnector.class)
                .postJsonAsync("accounting/contract/update", contractMap);
        logger.log(Level.INFO, "completed");
    }

    public Date setContractForTermination(Contract c, String terminationReason) {
       return setContractForTermination(c, terminationReason, new HashMap<>());
    }

    public Date setContractForTermination(Contract c, String terminationReason, Map<String, Object> m) {
        logger.log(Level.SEVERE, "ContractController setContractForTermination contract status: " + c.getStatus());
        logger.log(Level.SEVERE, "ContractController setContractForTermination terminationReason: " + terminationReason);
        if (c.getStatus() == ContractStatus.CANCELLED) return null;

        Map<String, Object> map = calculateDateOfTermination(c, m);
        Date dateOfTermination = (Date) map.get("dateOfTermination");

        String apiUrl = "/clientmgmt/contractTerminationNew/scheduleContractForTermination";

        Map<String, Object> body = new HashMap<>();
        body.put("id", c.getId());
        body.put("scheduledDateOfTermination", DateUtil.formatDateDashedWithTimeV2(dateOfTermination));
        body.put("whoTerminated", "COMPANY");
        if (map.containsKey("skipPEDCalculation")) {
            body.put("skipPEDCalculation", map.get("skipPEDCalculation"));
        }

        // ACC-2427
        if (terminationReason.equals("Due bounced payment"))
            body.put("isFromBouncingFlow", Boolean.TRUE);

        body.put("terminationType", "CANCELLATION");

        PicklistItem item = PicklistHelper.getItem(AccountingModule.PICKLIST_TERMINATION_REASON_LIST, terminationReason);

        Map<String, Object> termList = new HashMap<>();
        termList.put("id", item.getId());
        body.put("reasonOfTerminationList", termList);

        logger.log(Level.SEVERE, "ContractController setContractForTermination body: " + body);
        moduleConnector.postJsonAsync(apiUrl, body);
        logger.log(Level.SEVERE, "ContractController setContractForTermination Done");

        return dateOfTermination;
    }

    // ACC-9337
    public Map<String, Object> calculateDateOfTermination(Contract c, Map<String, Object> m) {

        Map<String, Object> res = new HashMap<>();
        res.put("skipPEDCalculation", false);

        // Default value
        Date dateOfTermination = new LocalDate(c.getPaidEndDate()).isAfter(new LocalDate()) ?
                c.getPaidEndDate() :
                new Date();
        res.put("dateOfTermination", dateOfTermination);
        if (!m.containsKey("flowName")) return res;

        switch ((String)m.get("flowName")) {
            case "Extension_Flow":
                int day = Utils.parseValue(Setup.getParameter(
                        Setup.getCurrentModule(),
                                AccountingModule.PARAMETER_SCHEDULED_DAY_FOR_TERMINATION_IN_EXTENSION_FLOW),
                        Integer.class);

                dateOfTermination = new DateTime().getDayOfMonth() > day ?  // Check if the day in the past
                                new DateTime().toDate() :
                                new DateTime().withDayOfMonth(day).toDate();

                dateOfTermination = Stream.of(dateOfTermination, c.getPaidEndDate()).max(Date::compareTo).get();

                res.put("skipPEDCalculation", true);
                res.put("dateOfTermination", dateOfTermination);
                break;
        }
        return res;
    }

    // ACC-6671
    public Housemaid getLastAttachedHousemaid(Contract contract) {

        if (contract.getHousemaid() != null) return contract.getHousemaid();

        HistorySelectQuery<Contract> q = new HistorySelectQuery<>(Contract.class);
        q.filterBy("id", "=", contract.getId());
        q.filterBy("housemaid", "is not null", null);
        q.sortBy("lastModificationDate", false, true);
        q.setLimit(1);

        List<Contract> l = q.execute();
        return l.isEmpty() ? null :
                Setup.getRepository(HousemaidRepository.class).findOne(
                            l.get(0).getHousemaid().getId());
    }

    @Transactional
    public void updatePayingViaCreditCardFlag(Contract c, boolean payingViaCreditCard) {

        updatePayingViaCreditCardFlag(c, payingViaCreditCard, false);
    }

    @Transactional
    public void updatePayingViaCreditCardFlag(Contract c, boolean payingViaCreditCard, boolean removeOMAFlag) {
        updatePayingViaCreditCardFlag(c, payingViaCreditCard, removeOMAFlag, true);
    }

    @Transactional
    public void updatePayingViaCreditCardFlag(Contract c, boolean payingViaCreditCard, boolean removeOMAFlag, boolean allowRecurring) {
        if (c.isPayingViaCreditCard() == payingViaCreditCard) return;

        logger.info("contract id: " + c.getId() + "; payingViaCreditCard: " + payingViaCreditCard);

        // ACC-8954 Remove IPAM Info if Exist
        if (payingViaCreditCard && ContractService.isIpam(c)) {
            c.removeBaseAdditionalInfo(Contract.ADDITIONAL_INFO_IS_IPAM_RUNNING);
        }

        AccountingEntityProperty a = accountingEntityPropertyRepository.findByKeyAndOriginAndDeletedFalse(Contract.CHANGE_TO_PAYING_VIA_CREDIT_CARD_DATE, c);
        logger.info("accountingEntityProperty id: " + (a == null ? "NULL" : a.getId()));
        if (payingViaCreditCard && a == null) {
            a = new AccountingEntityProperty();
            a.setOrigin(c);
            a.setKey(Contract.CHANGE_TO_PAYING_VIA_CREDIT_CARD_DATE);
            a.setValue(DateUtil.formatDateDashedWithTimeV3(new Date()));
            accountingEntityPropertyRepository.save(a);
        } else if (!payingViaCreditCard) {
            accountingEntityPropertyRepository.deleteByKeyAndOrigin(Contract.CHANGE_TO_PAYING_VIA_CREDIT_CARD_DATE, c);
        }

        Map<String, Object> m = new HashMap<>();
        m.put("id", c.getId());
        m.put("payingViaCreditCard", payingViaCreditCard);
        if (removeOMAFlag) {
            m.put("isOneMonthAgreement", false);
        }

        if (allowRecurring && payingViaCreditCard) {
            m.put("allowRecurring", true);
        }

        if (!payingViaCreditCard) {
            Setup.getApplicationContext()
                    .getBean(ContractPaymentTermServiceNew.class)
                    .deleteCreditCardToken(c.getActiveContractPaymentTerm(), false);
        }

        updateContractFromClientMgtAsync(m);
    }

    public Map<String, Object> getMaidCcCancellationInfo(Contract c, Date dateOfTermination, Date scheduledDateOfTermination) {
        // ACC-3843
        Map<String, Object> proratedContractConditions = Setup.getApplicationContext()
                .getBean(ClientMessagingAndRefundService.class)
                .checkProratedContractConditions(
                        c, dateOfTermination);

        boolean returnedMaidPreviousMonth = (boolean) proratedContractConditions.get("returnedMaidPreviousMonth");
        boolean clientCancelledWithinXDays = (boolean) proratedContractConditions.get("clientCancelledWithinXDays");

        Date d = clientCancelledWithinXDays || returnedMaidPreviousMonth ?
                new DateTime().minusMonths(1).dayOfMonth().withMaximumValue().toDate() :
                dateOfTermination != null ?  // revised flow
                        dateOfTermination :
                        scheduledDateOfTermination;

        proratedContractConditions.put("correctedBalance", getCorrectedBalance(c.getId(), DateUtil.formatDateDashed(d)));
        return proratedContractConditions;
    }

    public void updatePaidEndDate(Contract c) {
        Setup.getApplicationContext().getBean(BackgroundTaskService.class)
                .create(new BackgroundTask.builder(
                        "updatePaidEndDate_" + c.getId(),
                        "accounting",
                        "contractService",
                        "updatePaidEndDate")
                        .withRelatedEntity("Contract", c.getId())
                        .withParameters(
                                new Class[] {Long.class},
                                new Object[] {c.getId()})
                        .withQueue(BackgroundTaskQueues.NormalOperationsQueue)
                        .build());
    }

    public void updatePaidEndDate(Long id) throws Exception {
        fixAdjustedEndDate(contractRepository.findOne(id), false);
    }

    public void fixAdjustedEndDate(Contract contract, boolean removeOMAFlag) throws Exception {
        if (!checkContractType(contract)) {
            logger.info("contract id: " + contract.getId());
            Setup.getApplicationContext()
                    .getBean(InterModuleConnector.class)
                    .call("clientmgmt", "paymentService", "fixAdjustedEndDateFromAcc",
                            Map.class, new Class[] { Long.class, Boolean.class },
                            new Object[] { contract.getId(), removeOMAFlag});
            return;
        }

        Setup.getRepository(PaymentRepository.class).flush();

        logger.log(Level.SEVERE, "Contract end dates for " + contract.getId() + " are calculated using new Adj end date algorithm ");
        updateEndDate(contract, removeOMAFlag);
    }

    public static Boolean checkContractType(Contract contract) {
        PicklistItem contractProspectType = PicklistHelper.getItem("ProspectType", "maidvisa.ae_prospect");
        return contract.getContractProspectType().getId().equals(contractProspectType.getId()) ||
                (contract.getContractFeesType() != null && contract.getContractFeesType().equals(ContractFeesType.NO_ADJUSTED_END_DATE));

    }

    @Transactional
    public static void updateEndDate(Contract c, boolean removeOMAFlag) {
        try {
            java.util.Date currentAdjustedEndDate = c.getAdjustedEndDate();
            java.util.Date currentPaidEndDate = c.getPaidEndDate();

            logger.log(Level.SEVERE, "################## updateEndDate for contract : " + c.getId());
            logger.log(Level.SEVERE, "################## currentAdjustedEndDate : " + currentAdjustedEndDate);
            logger.log(Level.SEVERE, "################## currentPaidEndDate : " + currentAdjustedEndDate);


            PaymentRepository paymentRepository = Setup.getRepository(PaymentRepository.class);
            ContractRepository contractRepository = Setup.getRepository(ContractRepository.class);
            PicklistItem maidVisaProspectType = PicklistHelper.getItem("ProspectType", "maidvisa.ae_prospect");
            PicklistItem maidccProspectType = PicklistHelper.getItem("ProspectType", "maids.cc_prospect");
            int ReceivedPaymentsCount = paymentRepository.countByContractAndTypeOfPaymentAndStatusAndAmountPositive(c,
                    Setup.getRepository(PicklistItemRepository.class).findByListAndCodeIgnoreCase(
                            Setup.getRepository(PicklistRepository.class).findByCode("TypeOfPayment"),
                            "monthly_payment"),
                    PaymentStatus.RECEIVED) ;

            int pdpPaymentsCount = paymentRepository.countByContractAndTypeOfPaymentAndStatusAndAmountPositive(c,
                    Setup.getRepository(PicklistItemRepository.class).findByListAndCodeIgnoreCase(
                            Setup.getRepository(PicklistRepository.class).findByCode("TypeOfPayment"),
                            "monthly_payment"),
                    PaymentStatus.PDC);


            //CM-1870
            Set<String> typesOfPaymentsReceived = new HashSet<>();
            Set<String> typesOfPaymentsPDP= new HashSet<>();
            List<Payment> affectPaidEndDatePayments = paymentRepository.
                    findAllByContractAndAmountPositiveAndNotMonthlyPaymentAndAffectsPaidEndDate(c);

            //Received
            for (Payment payment : affectPaidEndDatePayments) {
                if (!typesOfPaymentsReceived.contains(payment.getTypeOfPayment().getCode())
                        && payment.getStatus().equals(PaymentStatus.RECEIVED))
                    typesOfPaymentsReceived.add(payment.getTypeOfPayment().getCode());
            }

            //PDC
            for (Payment payment : affectPaidEndDatePayments) {
                if (!typesOfPaymentsPDP.contains(payment.getTypeOfPayment().getCode())
                        && !typesOfPaymentsReceived.contains(payment.getTypeOfPayment().getCode())
                        && payment.getStatus().equals(PaymentStatus.PDC)) {
                    typesOfPaymentsPDP.add(payment.getTypeOfPayment().getCode());
                }
            }

            ReceivedPaymentsCount += typesOfPaymentsReceived.size();
            pdpPaymentsCount += typesOfPaymentsPDP.size();

            if (ReceivedPaymentsCount == 0 && pdpPaymentsCount==0) {
                return;
            }
            //Jirra CM-526
            int refundedPaymentsCount =
                    paymentRepository.countByContractAndTypeOfPaymentAndStatusAndAmountPositive(
                            c,
                            Setup.getRepository(PicklistItemRepository.class).findByListAndCodeIgnoreCase(
                                    Setup.getRepository(PicklistRepository.class).findByCode("TypeOfPayment"),
                                    "paid_the_client_-_refund"),
                            PaymentStatus.RECEIVED);


            int refundedDays = 0;
            List<Payment> partialRefundedPayments =
                    paymentRepository.findByContractAndTypeOfPaymentAndStatusAndAmountPositive(
                            c,
                            Setup.getRepository(PicklistItemRepository.class).findByListAndCodeIgnoreCase(
                                    Setup.getRepository(PicklistRepository.class).findByCode("TypeOfPayment"),
                                    "partial_mp_refunded_to_client"),
                            PaymentStatus.RECEIVED);

            if (partialRefundedPayments != null)
                for (Payment partialRefundedPayment : partialRefundedPayments) {
                    LocalDate date = new LocalDate(partialRefundedPayment.getDateOfPayment());
                    java.sql.Date monthStart = new java.sql.Date(date.dayOfMonth().withMinimumValue().toDate().getTime());
                    java.sql.Date monthEnd = new java.sql.Date(date.dayOfMonth().withMaximumValue().toDate().getTime());
                    List<Payment> monthlyPayments =
                            paymentRepository.findMonthlyPaymentByContractAndDateOfPaymentBetween(
                                    c, monthStart, monthEnd);
                    if (monthlyPayments != null && !monthlyPayments.isEmpty()) {
                        Payment monthlyPayment = monthlyPayments.get(0);
                        Double dailyCost = monthlyPayment.getAmountOfPayment() / 30.4;
                        logger.log(Level.SEVERE, "################## Prtial Refund Amount: "+partialRefundedPayment);
                        logger.log(Level.SEVERE, "################## Daily Cost: "+dailyCost);
                        refundedDays += (int) Math.round(partialRefundedPayment.getAmountOfPayment() / dailyCost);
                    }
                }
            logger.log(Level.SEVERE, "################## Totla Refunded Days: "+refundedDays);
            logger.log(Level.SEVERE, "################## Totla Refunded Months: "+refundedPaymentsCount);
            LocalDate startDate = new LocalDate(c.getStartOfContract());
            logger.log(Level.SEVERE, "################## start Date is " + startDate);
            // ADDED BY ANAS ALKASSAR CM-1300 (SUB CM-1318)
            if(c.getProRatedPlusMonth() && !c.getSpecialProRated()){
                startDate = startDate.plusMonths(1).dayOfMonth().withMinimumValue();
                logger.log(Level.SEVERE, "################## Contract is proRated + 1 month ");
                logger.log(Level.SEVERE, "################## start Date after proRate + 1 month" + startDate);
            }
            else if (c.getIsProRated()) {
                startDate = startDate.dayOfMonth().withMinimumValue();
                logger.log(Level.SEVERE, "################## Contract is proRated ");
                logger.log(Level.SEVERE, "################## start Date after proRate " + startDate);
            } else {
                ContractPaymentTermRepository termRepo = Setup.getRepository(ContractPaymentTermRepository.class);
                List<ContractPaymentTerm> terms = termRepo.findByContractAndIsActiveOrderByCreationDateDesc(c, true);
                if (!terms.isEmpty()) {
                    if (terms.get(0).getProRatedDays() != null) {
                        startDate = startDate.plusDays(terms.get(0).getProRatedDays());
                    }
                }
            }

            if (c.getContractProspectType().getId().equals(maidccProspectType.getId())
                    && c.getContractFeesType().equals(ContractFeesType.NO_ADJUSTED_END_DATE)) {
                logger.log(Level.SEVERE, "Contract is Maidcc new ");
                updateContractDatesForMaidccNew(c, startDate, ReceivedPaymentsCount, pdpPaymentsCount, refundedPaymentsCount, refundedDays);
            } else if (c.getContractProspectType().getId().equals(maidVisaProspectType.getId())) {
                logger.log(Level.SEVERE, "Contract is MaidVisa");
                updateContractDatesForMaidVisaNew(c, startDate, ReceivedPaymentsCount, pdpPaymentsCount, refundedPaymentsCount, refundedDays);
            }
            logger.log(Level.SEVERE, "################## AdjustedEndDate : " + c.getAdjustedEndDate());
            logger.log(Level.SEVERE, "################## PaidEndDate : " + c.getPaidEndDate());
            if (!DateUtil.checkIfInSameDay(currentAdjustedEndDate , c.getAdjustedEndDate()) ||
                    !DateUtil.checkIfInSameDay(currentPaidEndDate , c.getPaidEndDate())) {
                if (removeOMAFlag) {
                    contractRepository.saveAndFlush(c);
                } else {
                    contractRepository.silentSave(c);
                    contractRepository.flush();
                }
            }
        } catch (Throwable ex) {
            logger.log(Level.SEVERE, "an error occured when trying to update the end date of the payment contract", ex);
            throw ex;
        }

    }

    public static void updateContractDatesForMaidccNew(Contract contract,
                                                       LocalDate startDate,
                                                       int ReceivedPaymentsCount,
                                                       int PDCsPaymentsCount,
                                                       int refundedPaymentsCount,
                                                       int refundedDays) {
        //Adjusted End Date for New_Contract Maids.cc:: Last day covered by the LAST PDC.
        logger.log(Level.SEVERE, "################## Start of contract param: "+startDate);
        logger.log(Level.SEVERE, "################## ReceivedPaymentsCount: "+ReceivedPaymentsCount);
        logger.log(Level.SEVERE, "################## PDCsPaymentsCount: "+PDCsPaymentsCount);
        LocalDate endDate = startDate.plusMonths(ReceivedPaymentsCount + PDCsPaymentsCount);
        logger.log(Level.SEVERE, "################## endDate: "+endDate);
        //Jirra CM-526
        logger.log(Level.SEVERE, "################## refundedPaymentsCount: "+refundedPaymentsCount);
        logger.log(Level.SEVERE, "################## refundedDays: "+refundedDays);

        endDate = endDate.minusMonths(refundedPaymentsCount);
        endDate = endDate.minusDays(refundedDays);
        endDate = endDate.minusDays(1);
        //Jirra CM-515
        int unpaidVatDays = getUnpaidVatDays(contract);
        logger.log(Level.SEVERE, "################## unpaidVatDays: "+unpaidVatDays);
        endDate = endDate.minusDays(unpaidVatDays);
        logger.log(Level.SEVERE, "################## end date is " + endDate);

        contract.setEndOfContract(endDate.toDate());
        logger.log(Level.SEVERE, "################## End of Contract Date is  " + contract.getEndOfContract());

        contract.setAdjustedEndDate(endDate.toDate());
        logger.log(Level.SEVERE, "################## Adjusted end date  is  " + contract.getAdjustedEndDate());

        if(ReceivedPaymentsCount>0)
            contract.setPaidEndDate(startDate.plusMonths(ReceivedPaymentsCount)
                    .minusMonths(refundedPaymentsCount).minusDays(refundedDays + unpaidVatDays + 1).toDate());
        logger.log(Level.SEVERE, "################## Paid end date is  " + contract.getPaidEndDate());

    }


    public static int getUnpaidVatDays(Contract contract){

        try {
            VATRecordRepository vATRecordRepository =
                    Setup.getRepository(VATRecordRepository.class);
            List<VATRecord> vATRecords = vATRecordRepository.findByContract(contract);

            if (vATRecords != null
                    && !vATRecords.isEmpty()
                    && vATRecords.get(0).getTotalSmsCount() > 0
                    && vATRecords.get(0).getIsApprovedByClient() == false){
                String vATProjectDateStr = Setup.getParameter(Setup.getModule(AccountingModule.CLIENT_MGMT_MODULE_CODE), "VAT_PROJECT_DATE");
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                java.util.Date vATProjectDate = dateFormat.parse(vATProjectDateStr);
                PaymentRepository paymentRepository = Setup.getRepository(PaymentRepository.class);
                PicklistItem monthlyPayment = PicklistHelper.getItem("TypeOfPayment", "monthly_payment");
                List<Payment> payments =
                        paymentRepository.findByContractAndStatusAndDateOfPaymentGreaterThanAndIgnoreVATAndVatPaidByClientAndTypeOfPayment(
                                contract, PaymentStatus.RECEIVED,
                                new java.sql.Date(vATProjectDate.getTime()),
                                false, false, monthlyPayment);

                Double totalNotCoveredDays = 0.0;
                for (Payment p : payments){
                    Double amountWithVAT = p.getAmountOfPayment() * 1.05;
                    Double dailyCost = amountWithVAT / 30.4;
                    Double vAT = amountWithVAT - p.getAmountOfPayment();
                    totalNotCoveredDays += vAT/dailyCost;
                }
                Long notCoveredDays = Math.round(totalNotCoveredDays);
                return notCoveredDays.intValue();
            }
        } catch (ParseException ex) {
            Logger.getLogger(PaymentUtil.class.getName()).log(Level.SEVERE, null, ex);
        }
        return 0;
    }

    public static void updateContractDatesForMaidVisaNew(Contract contract,
                                                         LocalDate startDate,
                                                         int ReceivedPaymentsCount,
                                                         int PDCsPaymentsCount,
                                                         int refundedPaymentsCount,
                                                         int refundedDays) {
        //CM-1870
        int monthlyPaymentsCount = ReceivedPaymentsCount + PDCsPaymentsCount;
        logger.log(Level.SEVERE, "################## updateEndDate for contract monthlyPaymentsCount : " + monthlyPaymentsCount);


        LocalDate startOfContract = new LocalDate(contract.getStartOfContract());

        logger.log(Level.SEVERE, "################## updateEndDate for contract startOfContract : " + DateUtil.formatDate(startOfContract.toDate()));
        logger.log(Level.SEVERE, "################## updateEndDate for contract refundedPaymentsCount : " + refundedPaymentsCount);

        if(contract.getProRatedPlusMonth() && !contract.getSpecialProRated())
            monthlyPaymentsCount +=1;

        if(!contract.getIsRenewal())
            monthlyPaymentsCount -=1;
//        LocalDate endDate = startOfContract.plusMonths(monthlyPaymentsCount - refundedPaymentsCount);
        LocalDate endDate = startOfContract.plusMonths(monthlyPaymentsCount);
        LocalDate paidEndDate = startOfContract.plusMonths(monthlyPaymentsCount - PDCsPaymentsCount);

        logger.log(Level.SEVERE, "################## updateEndDate for contract endDate : " + DateUtil.formatDate(endDate.toDate()));
        logger.log(Level.SEVERE, "################## updateEndDate for contract paidEndDate : " + DateUtil.formatDate(paidEndDate.toDate()));


        endDate = endDate.minusMonths(refundedPaymentsCount);
        endDate = endDate.minusDays(refundedDays);

        paidEndDate = paidEndDate.minusMonths(refundedPaymentsCount);
        paidEndDate = paidEndDate.minusDays(refundedDays);

        Calendar endCalendar=Calendar.getInstance();
        endCalendar.setTime(endDate.toDate());
        endDate = endDate.withDayOfMonth(endCalendar.getActualMaximum(Calendar.DAY_OF_MONTH));

        Calendar paidCalendar=Calendar.getInstance();
        paidCalendar.setTime(paidEndDate.toDate());
        paidEndDate = paidEndDate.withDayOfMonth(paidCalendar.getActualMaximum(Calendar.DAY_OF_MONTH));

        contract.setAdjustedEndDate(endDate.toDate());
        contract.setEndOfContract(endDate.toDate());

        if(ReceivedPaymentsCount>0)
            contract.setPaidEndDate(paidEndDate.toDate());
    }

    public static boolean isEligibleForTokenizationViaContract(Contract c) {
        return c.getStatus().equals(ContractStatus.ACTIVE) &&
                c.isPayingViaCreditCard() && c.getAllowRecurring() &&
                ClientPayingViaCreditCardService.erpAllowedRecurring();
    }

    public Map<String, Object> getContractCreditCardInfo(Contract contract ) {

        Map<String, Object> sourceInfo = contract.getActiveContractPaymentTerm().getSourceInfo();

        return new HashMap<String, Object>(){{
            put("cardNumber", sourceInfo.getOrDefault("cardNumber", ""));
            put("expiryDate", sourceInfo.getOrDefault("expiryDateFormatted", ""));
            put("payingViaCreditCard", contract.isPayingViaCreditCard());
            put("allowRecurring", contract.getAllowRecurring());
            put("allowDeleteToken", CurrentRequest.getUser() != null &&
                    CurrentRequest.getUser().hasPosition(ContractPaymentTermController.ACC_DELETE_CREDIT_CARD_TOKEN_POSITION));
            put("showWarningMessageForIPAM", ContractService.isIpam(contract));

            boolean showExtensionFlowToggle = contract.isMaidVisa() && ExtensionFlowService.erpAllowedExtensionFlow() &&
                    flowProcessorService.isPayingViaCreditCard(contract);
            put("showExtensionFlowToggle", showExtensionFlowToggle);
            put("isExtensionFlowEnabled", showExtensionFlowToggle && !ContractService.isExtensionFlowDisabled(contract));
        }};
    }

    // ACC-7105
    public void confirmUpdateSalaryAndPaymentForMaid(Contract contract) {
        ContractPaymentTerm cpt = contract.getActiveContractPaymentTerm();

        double workerSalary = contract.getHousemaid() != null && contract.getHousemaid().getNationality() != null ?
                (double) getWorkerSalaryAndDesignationByOecNationalitySetup(contract.getHousemaid().getNationality().getId())
                        .get("workerSalary") :
                0.0D;

        if (workerSalary < 1) return;

        Map<String, Object> updatedContract = new HashMap<>();
        updatedContract.put("id", contract.getId());
        updatedContract.put("workerSalary", workerSalary);

        updateContractFromClientMgt(updatedContract);

        disableOldPayTabsLinksOnWorkerSalaryUpdated(contract);

        // Todo To be checked with Shaban
        // Paying via cc
        if (flowProcessorService.isPayingViaCreditCard(contract)) {
            logger.info("client paying via cc or has running IPAM: " + contract.getId());
            return;
        }

        List<DirectDebit> dds = Setup.getRepository(DirectDebitRepository.class).
                getMonthlyDirectDebitIncludedWorkerSalary(
                            contract,
                            Arrays.asList(
                                    DirectDebitStatus.CANCELED,
                                    DirectDebitStatus.EXPIRED,
                                    DirectDebitStatus.PENDING_FOR_CANCELLATION));

        DirectDebitCancellationService directDebitCancellationService =
                Setup.getApplicationContext().getBean(DirectDebitCancellationService.class);
        for (DirectDebit dd : dds) {
            logger.info("cancelling dd with id -> " + dd.getId());
            directDebitCancellationService.cancelWholeDD(
                    dd, false, DirectDebitCancellationToDoReason.WORKER_SALARY_CHANGED);
        }

        logger.info("create DD for cpt : " + cpt.getId());
        createBGToGenerateDD(contract);
    }

    // ACC-7105
    public Map<String, Object> getWorkerSalaryAndDesignationByOecNationalitySetup(Long nationalityId) {
        final String oecUrl = AccountingModule.VISA_MODULE_URL + "/oec-nationality-setup/search";
        Map<String, Object> r = new HashMap<>();
        r.put("workerSalary", 0.0D);
        r.put("designation", "");

        try {
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("nationalityId", nationalityId);

            Map response = Setup.getApplicationContext()
                    .getBean(InterModuleConnector.class)
                    .postJson(oecUrl, requestBody, Map.class);

            double nepaliBasicSalary = 0.0;
            double allowance = 0.0;

            if (response.get("validDesignations") != null) {
                List<Map<String, Object>> validDesignations = (List<Map<String, Object>>) response.get("validDesignations");
                if (!validDesignations.isEmpty()) {
                    r.put("designation", validDesignations.stream().map(d -> d.get("label"))
                            .collect(Collectors.toList()));
                }
            }

            if (response.get("allowance") != null) {
                Object allowanceObj = response.get("allowance");
                if (allowanceObj instanceof Number) {
                    allowance = ((Number) allowanceObj).doubleValue();
                }
            }

            if (response.get("basicSalary") != null) {
                Object basicSalaryObj = response.get("basicSalary");
                if (basicSalaryObj instanceof Number) {
                    nepaliBasicSalary = ((Number) basicSalaryObj).doubleValue();
                }
            }

            r.put("workerSalary", allowance + nepaliBasicSalary);
        } catch (Exception e) {
            e.printStackTrace();
        }

        return r;
    }

    private void disableOldPayTabsLinksOnWorkerSalaryUpdated(Contract c) {

        LocalDate discountStartDate = new LocalDate(calculateDiscountsWithVatService
                .getDiscountStartDateInMillis(c.getStartOfContract(),
                        c.getIsProRated(), c.getActiveContractPaymentTerm()));
        List<ContractPaymentConfirmationToDo> toDos = contractPaymentConfirmationToDoRepository
                .findByContractAndDisabledAndWorkerSalaryAndSourceAndDateOfPayment(
                        c,
                        discountStartDate.isAfter(new LocalDate().plusMonths(1).dayOfMonth().withMinimumValue()) ?
                            discountStartDate.toDate() :
                            new LocalDate().plusMonths(1).dayOfMonth().withMinimumValue().toDate());

        if (toDos.isEmpty()) return;

        toDos.forEach(d -> {
            logger.info("todo id: " + d.getId());
            d.setDisabled(true);
        });
        contractPaymentConfirmationToDoRepository.save(toDos);

        Setup.getApplicationContext()
                .getBean(MessagingService.class)
                .createDisableNotificationBGT(
                        Setup.getRepository(DisablePushNotificationRepository.class)
                                .findActiveNotificationsByDDMessagingTypeAndOwnerTypeAndOwnerId(
                                        toDos.stream().map(todo -> todo.getId().toString()).collect(Collectors.toList())),
                        "Disable on worker salary updated");
    }

    public void createBGToGenerateDD(Contract contract) {
        Setup.getApplicationContext()
                .getBean(BackgroundTaskService.class)
                .create(new BackgroundTask.builder(
                        "generateDDForContractAfterSalaryChanged_" + contract.getId(),
                        "accounting",
                        "contractService",
                        "generateDDAfterSalaryChanged")
                        .withRelatedEntity(contract.getEntityType(), contract.getId())
                        .withParameters(new Class[]{Long.class},
                                new Object[]{contract.getId()})
                        .withQueue(BackgroundTaskQueues.NormalOperationsQueue)
                        .withDelay(60 * 1000L)
                        .build());
    }

    public void generateDDAfterSalaryChanged(Long contractId) throws Exception {

        Contract contract = contractRepository.findOne(contractId);
        ContractPaymentTerm cpt = contract.getActiveContractPaymentTerm();
        generateDDAfterSalaryChanged(cpt, new LocalDate(calculateDiscountsWithVatService
                .getDiscountStartDateInMillis(contract.getStartOfContract(),
                        contract.getIsProRated(), cpt)));
    }

    public List<ContractPayment> generateDDAfterSalaryChanged(ContractPaymentTerm cpt, LocalDate startDiscountDate) throws Exception {

        cpt.setForceGenerateDds(true);
        List<ContractPayment> payments = (List<ContractPayment>) ((Map<String, Object>) contractPaymentTermServiceNew
                .getContractPaymentTermByContractWithDirectDebitPayments(cpt)).get("payments");
        logger.info("contractId: " + cpt.getContract().getId() + "; payments size: " + payments.size());

        // 1-Remove all payments before startDiscountDate or non-monthly
        payments.removeIf(p -> new LocalDate(p.getDate()).isBefore(startDiscountDate) || !PaymentHelper.isMonthlyPayment(p.getPaymentType()));

        // 2-Remove all received payments
        payments = Setup.getApplicationContext()
                .getBean(ContractPaymentService.class)
                .getUniqueAndSortedPayments(cpt.getContract(), payments, null);
        Collections.reverse(payments);

        // 3-Remove the relation Payment and Direct debit
        payments.forEach(payment -> payment.setDirectDebit(null));

        // 4-Add first payment to DDA
        ContractPayment payment = payments.stream()
                .filter(p -> PaymentHelper.isMonthlyPayment(p.getPaymentType()))
                .findFirst()
                .orElse(null);

        logger.info("DDA paymentDate: " + (payment == null || payment.getDate() == null ? "NULL" :
                new LocalDate(payment.getDate()).toString("yyyy-MM-dd") + ", isOneTime: " + payment.isOneTime()));
        if (payment != null && !payment.isOneTime()) {
            payment.setOneTime(true);
        }

        logger.info("payments size: " + payments.size());
        if (payments.isEmpty()) return new ArrayList<>();

        // 5-Generate new DDs
        List<DirectDebitSignature> signatures = (List<DirectDebitSignature>) directDebitSignatureService.
                getLastSignatureType(cpt, true, false).get("currentSignatures");

        contractPaymentTermServiceNew.updateContractPaymentTermWithPayments(
                cpt, true, false, payments,
                signatures == null ? new ArrayList<>() :
                        directDebitSignatureService.getSignatureAttachmentsOnly(signatures),
                false, signatures == null, true,
                false, false, true, false, new HashMap<>());

        return payments;
    }

    public void flagOldActiveCcAndPayingViaCcContractsAsRecurring() {
        logger.info("start");

        Page<Contract> l;
        Long lastId = -1L;

        do {
            l = contractRepository.findActiveCcAndPayingViaCcContract(lastId, PageRequest.of(0, 200));
            l.getContent().forEach(c -> {
                logger.info("contract id: " + c.getId());
                try {
                   c.setAllowRecurring(true);
                   contractRepository.silentSave(c);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            });

            if (!l.getContent().isEmpty()) {
                lastId = l.getContent().get(l.getContent().size() - 1).getId();
            }
        } while (!l.getContent().isEmpty());

        logger.info("finish");
    }

    // ACC-8662
    public boolean validateAndStartPreventCreateOtherDds(ContractPaymentTerm cpt) {
        return validateAndStartPreventCreateOtherDds(cpt, false);
    }

    public boolean validateAndStartPreventCreateOtherDds(ContractPaymentTerm cpt, boolean isPayingViaCC) {

        if (cpt.getContractPaymentTypes().stream()
                .noneMatch(t -> t.getType().hasTag(AbstractPaymentTypeConfig.PREVENT_OTHER_DDS_TAG))) {
            return false;
        }

        // Find Prevent Create Other DDs Property (deleted or not)
        AccountingEntityProperty a = accountingEntityPropertyRepository.findByKeyAndOrigin(Contract.PROPERTY_PREVENT_CREATE_OTHER_DDS, cpt.getContract())
                .stream()
                .findFirst()
                .orElse(null);

        logger.info("accountingEntityProperty id: " + (a == null ? "NULL" : a.getId()));

        // If exist return false (Not Start Prevent Create Other DDs) and (if Paying via CC and not deleted -> deleted)
        if (a != null) {
            // Stop Prevent Create Other Dds if request Paying via CC API at the second.
            if (isPayingViaCC && !a.getIsDeleted()) {
                logger.info("isDeleted: " + a.getIsDeleted());
                a.setIsDeleted(true);
                accountingEntityPropertyRepository.save(a);
                return false;
            }
            return !a.getIsDeleted();
        }

        // If not exist => create new one and return true (Start Prevent Create Other DDs)
        a = new AccountingEntityProperty();
        a.setOrigin(cpt.getContract());
        a.setKey(Contract.PROPERTY_PREVENT_CREATE_OTHER_DDS);
        a.setValue(new LocalDate().toString("yyyy-MM-dd"));
        accountingEntityPropertyRepository.save(a);
        return true;
    }

    // ACC-8662
    public boolean hasPreventCreateOtherDds(Contract contract) {

        boolean isExists = accountingEntityPropertyRepository.existsByKeyInAndOriginAndDeletedFalse(
                Contract.PROPERTY_PREVENT_CREATE_OTHER_DDS, contract.getId());
        logger.info("contract id: " + contract.getId() + ", has Prevent Create Other Dds: "+ isExists);
        return isExists;
    }

    // ACC-8662
    public void stopPreventCreateOtherDdsIfHas(Contract contract) {
        AccountingEntityProperty a = accountingEntityPropertyRepository.findByKeyAndOriginAndDeletedFalse(
                Contract.PROPERTY_PREVENT_CREATE_OTHER_DDS, contract);
        logger.info("accountingEntityProperty id: " + (a == null ? "NULL" : a.getId()));

        if (a != null) {
            a.setIsDeleted(true);
            accountingEntityPropertyRepository.save(a);
        }
    }

    public static boolean isExtensionFlowDisabled(Contract contract) {
        return hasBaseAdditionalInfoByKey(contract.getId(), Contract.ADDITIONAL_INFO_EXTENSION_FLOW_DISABLED, "true");
    }

    public static boolean isIpam(Contract contract) {
        return hasBaseAdditionalInfoByKey(contract.getId(), Contract.ADDITIONAL_INFO_IS_IPAM_RUNNING, "true");
    }

    public static boolean hasBaseAdditionalInfoByKey(Long contractId, String key, String value) {

        String ownerType = "Contract";
        boolean hasBaseAdditionalInfoByKey = Setup.getRepository(BaseAdditionalInfoAccRepository.class)
                .existsByOwnerIdAndOwnerTypeAndInfoKeyAndInfoValue(contractId, ownerType, key, value);
        logger.info("owner id: " + contractId +
                "; ownerType: " + ownerType +
                "; key: " + key +
                "; value: " + value +
                "; hasBaseAdditionalInfoByKey: " + hasBaseAdditionalInfoByKey);
        return hasBaseAdditionalInfoByKey;
    }

    public void addAdditionalInfoForIpamFlow(Contract contract) {
        if (contract.hasBaseAdditionalInfo(Contract.ADDITIONAL_INFO_IS_IPAM_RUNNING)) return;

        contract.addBaseAdditionalInfo(new BaseAdditionalInfo(
                Contract.ADDITIONAL_INFO_IS_IPAM_RUNNING,
                "true",
                contract.getEntityType(),
                contract.getId()));

        contractRepository.silentSave(contract);
    }


    private Map<String, Date> getPaymentPeriodDates(Contract contract) {
        Map<String, Date> result = new HashMap<>();

        Date startDate = new LocalDate().dayOfMonth().withMinimumValue().toDate();
        Date endDate = new LocalDate().dayOfMonth().withMaximumValue().toDate();

        OneMonthAgreementFlowService oneMonthAgreementFlowService = Setup.getApplicationContext()
                .getBean(OneMonthAgreementFlowService.class);
        if (oneMonthAgreementFlowService.isPayingViaCreditCard(contract)) {
            startDate = oneMonthAgreementFlowService.getCurrentPaymentDate(contract).toDate();
            endDate = oneMonthAgreementFlowService.getLastDayInCurrentPayment(contract).toDate();
        }

        result.put("startDate", startDate);
        result.put("endDate", endDate);
        return result;
    }


    private Map<String, Object> getContractPaymentInfo(Contract contract, boolean returnAmountFromCTP) {
        Map<String, Object> result = new HashMap<>();
        ContractPaymentTerm cpt = contract.getActiveContractPaymentTerm();

        Map<String, Date> periodDates = getPaymentPeriodDates(contract);
        Date startDate = periodDates.get("startDate");
        Date endDate = periodDates.get("endDate");

        List<Object[]> payments = paymentRepository.findCurrentMonthPaymentsInfo(contract, startDate, endDate);
        List<Map<String, Object>> currentPayments = new ArrayList<>();
        Map<String, Object> currentPayment = new HashMap<>();

        if (!payments.isEmpty()) {
            for (Object[] row : payments) {
                currentPayments.add(new HashMap<String, Object>() {{
                    put("status", ((PaymentStatus) row[0]).getLabel());
                    put("amount", "AED " + String.format("%,.0f", (Double) row[1]));
                    put("amountValue", row[1]);
                    put("paymentType", row[2]);
                    put("paymentTypeCode", row[4]);
                    put("workerSalary", row[5]);
                    put("workerSalaryWithoutVAT", row[6]);
                    put("visaFees", row[7]);
                }});
            }
        } else {
            if(returnAmountFromCTP) {
                ContractPaymentType type = cpt.getContractPaymentTypes().stream()
                        .filter(t -> PaymentHelper.isMonthlyPayment(t.getType()))
                        .findFirst().orElse(null);
                currentPayment.put("paymentType", type != null ? type.getType().getName() : "");
                currentPayment.put("paymentTypeCode", type != null ? type.getType().getCode() : "");

                Double amount = type != null ?
                        (Double) Setup.getApplicationContext()
                                .getBean(CalculateDiscountsWithVatService.class)
                                .getAmountOfMonthlyPaymentAtTimeWithDiscounts(cpt, new LocalDate())
                                .get("amount") : 0D;

                currentPayment.put("status", "");
                currentPayment.put("amount", "AED " + String.format("%,.0f", amount));
                currentPayment.put("amountValue", amount);

                currentPayments.add(currentPayment);
            }
        }

        result.put("currentPayments", currentPayments);

        // Current payment (monthly payment)
        if (currentPayment.isEmpty()){
            payments = payments.stream().filter(p -> p[2].equals("monthly_payment")).collect(Collectors.toList());
            Double amount = !payments.isEmpty() ?
                    (Double) payments.get(0)[1] :
                    (Double) Setup.getApplicationContext()
                            .getBean(CalculateDiscountsWithVatService.class)
                            .getAmountOfMonthlyPaymentAtTimeWithDiscounts(cpt, new LocalDate())
                            .get("amount");

            currentPayment.put("amount", "AED " + String.format("%,.0f", amount));
            currentPayment.put("amountValue", amount);
            currentPayment.put("status", payments.isEmpty() ? "" : ((PaymentStatus) payments.get(0)[0]).getLabel().toLowerCase());
            /*currentPayment.put("paymentTypeCode", payments.isEmpty() ? "" : payments.get(0)[4]);
            currentPayment.put("workerSalary", payments.isEmpty() ? null : payments.get(0)[5]);
            currentPayment.put("workerSalaryWithoutVAT", payments.isEmpty() ? null : payments.get(0)[6]);*/
        }
        result.put("currentPayment", currentPayment);

        return result;
    }

    public Map<String, Object> getDetailedPaymentInfo(Contract c) {
        Map<String, Object> payments = new HashMap<>();
        Map<String, Object> result = new HashMap<>();

        addBasicPaymentInfo(c, payments);

        result.put("payments", payments);
        result.put("plan", getPaymentPlan(c.getActiveContractPaymentTerm()));

        result.put("paymentMethod", Setup.getApplicationContext()
                .getBean(FlowProcessorService.class)
                .isPayingViaCreditCard(c) ? "Credit Card" : "Monthly Bank Payment Form");

        return result;
    }

    private void addBasicPaymentInfo(Contract c, Map<String, Object> result) {
        Map<String, Object> paymentInfo = getContractPaymentInfo(c, false);
        List<Map<String, Object>> currentPayments = (List<Map<String, Object>>) paymentInfo.get("currentPayments");


        Map<String, Integer> paymentCounts = new HashMap<>();
        for (Map<String, Object> payment : currentPayments) {
            String type = (String) payment.getOrDefault("paymentTypeCode", "missing");
            paymentCounts.put(type.toLowerCase(), paymentCounts.getOrDefault(type, 0) + 1);
        }


        // Initialize all payment types with "missing" values
        result.put("currentMonthlyPaymentAmount", "missing");
        result.put("currentMonthlyPaymentStatus", "missing");
        result.put("currentSDRAmount", "missing");
        result.put("currentSDRStatus", "missing");
        result.put("currentPreCollectedSalaryAmount", "missing");
        result.put("currentPreCollectedSalaryStatus", "missing");
        result.put("currentPreCollectedSalaryNoVatAmount", "missing");
        result.put("currentPreCollectedSalaryNoVatStatus", "missing");
        result.put("currentTravelPaymentAmount", "missing");
        result.put("currentTravelPaymentStatus", "missing");
        result.put("currentOverstayFinesAmount", "missing");
        result.put("currentOverstayFinesStatus", "missing");
        result.put("currentMatchingFeeAmount", "missing");
        result.put("currentMatchingFeeStatus", "missing");
        result.put("currentWPSAmount", "missing");
        result.put("currentWPSStatus", "missing");

        handlePaymentTypes(result, currentPayments, paymentCounts, "monthly_payment", "currentMonthlyPayment");
        handlePaymentTypes(result, currentPayments, paymentCounts, "same_day_recruitment_fee", "currentSDR");
        handlePaymentTypes(result, currentPayments, paymentCounts, "pre_collected_payment", "currentPreCollectedSalary");
        handlePaymentTypes(result, currentPayments, paymentCounts, "pre_collected_payment_no_vat", "currentPreCollectedSalaryNoVat");
        handlePaymentTypes(result, currentPayments, paymentCounts, "travel_assist", "currentTravelPayment");
        handlePaymentTypes(result, currentPayments, paymentCounts, "overstay_fee", "currentOverstayFines");
        handlePaymentTypes(result, currentPayments, paymentCounts, "matching_fee", "currentMatchingFee");
        //handlePaymentTypes(result, currentPayments, paymentCounts, "monthly_payment", "currentWPS");
    }


    private void handlePaymentTypes(Map<String, Object> result, List<Map<String, Object>> currentPayments,
                                    Map<String, Integer> paymentCounts,
                                    String paymentTypeCode, String fieldPrefix) {
        int count = paymentCounts.getOrDefault(paymentTypeCode, 0);

        if (count > 1) {
            // Handle multiple payments of the same type
            int currentIndex = 1;
            for (Map<String, Object> payment : currentPayments) {
                String type = (String) payment.getOrDefault("paymentTypeCode", "missing");

                if (paymentTypeCode.equals(type)) {
                    if ("monthly_payment".equals(paymentTypeCode)) {
                        Double workerSalary = (Double) payment.get("workerSalary");
                        Double workerSalaryWithoutVAT = (Double) payment.get("workerSalaryWithoutVAT");
                        Double visaFees = (Double) payment.get("visaFees");
                        String formattedWorkerSalary = String.format("AED %,.0f", workerSalary != null ? workerSalary : (workerSalaryWithoutVAT != null ? workerSalaryWithoutVAT : 0.0));
                        String formattedVisaFees = String.format("AED %,.0f", visaFees);
                        result.put(fieldPrefix + "Amount" + currentIndex, formattedWorkerSalary);
                        result.put(fieldPrefix + "Status" + currentIndex, payment.getOrDefault("status", "missing"));
                        result.remove("currentMonthlyPaymentAmount");
                        result.remove("currentMonthlyPaymentStatus");

                        result.put("currentWPSAmount" + currentIndex, formattedVisaFees);
                        result.put("currentWPSStatus" + currentIndex, payment.getOrDefault("status", "missing"));

                        result.remove("currentWPSAmount");
                        result.remove("currentWPSStatus");

                    } else {
                        result.put(fieldPrefix + "Amount" + currentIndex, payment.getOrDefault("amount", "missing"));
                        result.put(fieldPrefix + "Status" + currentIndex, payment.getOrDefault("status", "missing"));
                        // remove the non index key
                        result.remove(fieldPrefix + "Amount");
                        result.remove(fieldPrefix + "Status");
                    }
                    currentIndex++;
                }
            }
        } else if (count == 1) {
            // Handle single payment of this type
            for (Map<String, Object> payment : currentPayments) {
                String type = (String) payment.getOrDefault("paymentTypeCode", "missing");

                if (paymentTypeCode.equals(type)) {
                    if ("monthly_payment".equals(paymentTypeCode)) {
                        Double workerSalary = (Double) payment.get("workerSalary");
                        Double workerSalaryWithoutVAT = (Double) payment.get("workerSalaryWithoutVAT");
                        String formattedWorkerSalary = String.format("AED %,.0f", workerSalary != null ? workerSalary : (workerSalaryWithoutVAT != null ? workerSalaryWithoutVAT : 0.0));
                        result.put(fieldPrefix + "Amount", formattedWorkerSalary);
                        result.put(fieldPrefix + "Status", payment.getOrDefault("status", "missing"));

                        Double visaFees = (Double) payment.get("visaFees");
                        String formattedVisaFees = String.format("AED %,.0f", visaFees);
                        result.put("currentWPSAmount", formattedVisaFees);
                        result.put("currentWPSStatus", payment.getOrDefault("status", "missing"));

                    } else {
                        result.put(fieldPrefix + "Amount", payment.getOrDefault("amount", "missing"));
                        result.put(fieldPrefix + "Status", payment.getOrDefault("status", "missing"));
                    }
                    break;
                }
            }
        }
    }

    public static Map<String, String> getPaymentPlan(ContractPaymentTerm cpt) {
        Map<String, String> paymentPlan = new HashMap<>();


        ContractPaymentTypeRepository contractPaymentTypeRepository = Setup.getRepository(ContractPaymentTypeRepository.class);
        Double wpsAmountWithVat = contractPaymentTypeRepository.findWpsAmountWithVatByContractId(cpt.getContract().getId());
        if(wpsAmountWithVat != null) {
            Double wpsAmountWithoutVat = Math.floor(DiscountsWithVatHelper.getAmountWithoutVat(wpsAmountWithVat));
            double wpsVat = wpsAmountWithVat - wpsAmountWithoutVat;
            paymentPlan.put("wpsProcessingAmount",
                    String.format("AED %.0f + AED %.0f VAT", wpsAmountWithoutVat, wpsVat));
        }

        for(Map<String, Object> p : PaymentReceiptHelper.getPaymentsReceiptTermForms(cpt)) {
            Map<String, Object> paymentInfo = (Map<String, Object>) p.get("paymentInfo");
            Double amountWithoutVat = p.get("amountWithoutVat") instanceof Long ?
                    ((Long) p.get("amountWithoutVat")).doubleValue() :
                    p.get("amountWithoutVat") instanceof Integer ?
                            ((Integer) p.get("amountWithoutVat")) : (Double) p.get("amountWithoutVat");
            Double amount = p.get("amount") instanceof Long ?
                    ((Long) p.get("amount")).doubleValue() :
                    p.get("amount") instanceof Integer ? ((Integer) p.get("amount")) : (Double) p.get("amount");

            String typeCode = (String) paymentInfo.get("typeCode");
            double vatValue = amount - amountWithoutVat;

            switch(typeCode) {
                case "same_day_recruitment_fee":

                    Integer frequency = (Integer) paymentInfo.getOrDefault("frequency", 0);

                    if (frequency > 0) {
                        paymentPlan.put("2yearVisaAmount",
                                String.format("AED %.0f + AED %.0f", amountWithoutVat, vatValue));
                    } else if (frequency == 0) {

                        int index = 1;
                        String key = "2yearOnetimeAmount";
                        while (paymentPlan.containsKey(index == 1 ? key : key + index)) {
                            index++;
                        }
                        paymentPlan.put(index == 1 ? key : key + index,
                                String.format("AED %.0f + AED %.0f", amountWithoutVat, vatValue));

                    }
                    break;
                case "pre_collected_payment":
                    paymentPlan.put("preCollectedPayment",
                            String.format("AED %.0f + AED %.0f VAT", amountWithoutVat, vatValue));
                    break;
                case "pre_collected_payment_no_vat":
                    paymentPlan.put("preCollectedPaymentNoVat",
                            String.format("AED %.0f + AED %.0f VAT", amountWithoutVat, vatValue));
                    break;
                case "travel_assist":
                    paymentPlan.put("travelAssistAmount",
                            String.format("AED %.0f + AED %.0f VAT", amountWithoutVat, vatValue));
                    break;
                case "matching_fee":
                    paymentPlan.put("matchingFee",
                            String.format("AED %.0f + AED %.0f VAT", amountWithoutVat, vatValue));
                    break;
                case "insurance":
                    if (paymentInfo.get("startsOn") != null) {
                    String dateStr = (String) paymentInfo.get("startsOn");
                    DateTime insuranceDate;
                    try {
                        insuranceDate = DateTimeFormat.forPattern("yyyy-MM-dd HH:mm:ss").parseDateTime(dateStr);
                    } catch (IllegalArgumentException e) {
                        insuranceDate = DateTimeFormat.forPattern("yyyy-MM-dd").parseDateTime(dateStr);
                    }

                    paymentPlan.put("secondYearInsuranceDate", insuranceDate.toString("MMM dd yyyy"));
                    paymentPlan.put("secondYearInsuranceAmount",
                            String.format("AED %.0f + AED %.0f VAT", amountWithoutVat, vatValue));
                }
                    break;
            }
        }

        // Add "missing" for any undefined values
        String[] requiredFields = {
                "wpsProcessingAmount", "2yearVisaAmount", "preCollectedPayment","preCollectedPaymentNoVat",
                "travelAssistAmount", "matchingFee", "secondYearInsuranceDate",
                "secondYearInsuranceAmount", "2yearOnetimeAmount"
        };

        for (String field : requiredFields) {
            if (!paymentPlan.containsKey(field)) {
                paymentPlan.put(field, "missing");
            }
        }

        return paymentPlan;
    }

    public Map<String, Object> getMergedPaymentAndCollectionFlowInfo(
            Contract contract, String requiredKeysParam, boolean isMaidVisa) {
        // Get data from both APIs
        Map<String, Object> paymentInfo = getDetailedPaymentInfo(contract);
        List<Map<String, Object>> collectionFlows = Setup.getApplicationContext()
                .getBean(CollectionFlowLogService.class)
                .getRunningFlowsMessagesByContractACC8311(contract);

        Map<String, Object> result = new HashMap<>();

        if (paymentInfo.containsKey("payments") && isMaidVisa) {
            result.putAll((Map<String, Object>) paymentInfo.get("payments"));
        }

        if (paymentInfo.containsKey("plan")  && isMaidVisa) {
            result.putAll((Map<String, Object>) paymentInfo.get("plan"));
        }

        if (paymentInfo.containsKey("paymentMethod") && isMaidVisa) {
            result.put("paymentMethod", paymentInfo.get("paymentMethod"));
        }

        if (collectionFlows != null && !collectionFlows.isEmpty()) {
            for (int i = 0; i < collectionFlows.size(); i++) {
                Map<String, Object> flow = collectionFlows.get(i);
                int index = i + 1;

                result.put(collectionFlows.size() == 1 ? "ongoingFlowDetails" : "ongoingFlowDetails" + index,
                        flow.get("flowName") + " " + flow.get("message"));

                result.put(collectionFlows.size() == 1 ? "cclink" : "cclink" + index,
                        flow.getOrDefault("ccLink", "missing"));

                result.put(collectionFlows.size() == 1 ? "signLink" : "signLink" + index,
                        flow.getOrDefault("signLink", "missing"));
            }
        }

        // Filter by required keys if specified
        if (requiredKeysParam != null && !requiredKeysParam.trim().isEmpty()) {
            result = filterByRequiredKeys(result, requiredKeysParam);
        }

        // ACC-9500
        result.putAll(addCustomKeysAcc9500(contract));

        return result;
    }

    private Map<String, Object> filterByRequiredKeys(Map<String, Object> data, String requiredKeysParam) {
        Map<String, Object> filteredResult = new HashMap<>();

        String[] keys = requiredKeysParam.split(",");
        for (String key : keys) {
            String trimmedKey = key.trim();

            if (data.containsKey(trimmedKey)) {
                filteredResult.put(trimmedKey, data.get(trimmedKey));
            }

            if (!trimmedKey.matches(".*\\d$")) {
                for (String dataKey : data.keySet()) {
                    if (dataKey.matches(trimmedKey + "\\d*") && !dataKey.equals(trimmedKey) && data.containsKey(dataKey)) {
                        filteredResult.put(dataKey, data.get(dataKey));
                    }
                }
            }
        }

        return filteredResult;
    }

    public static boolean isPreCollectedSalary(Contract contract) {
        return hasBaseAdditionalInfoByKey(contract.getId(), "preCollectedSalary", "true");
    }

    // ACC-9500
    private Map<String, Object> addCustomKeysAcc9500(Contract contract) {
        Map<String, Object> result = new HashMap<>();

        result.put("preCollectedSalary", String.valueOf(ContractService.isPreCollectedSalary(contract)));
        result.put("filipinaCreditNoteCond", "missing");
        result.put("nonFilipinaCreditNoteCond", "missing");

        Map<String, Object> m = Setup.getApplicationContext()
                .getBean(PaymentService.class)
                .calculateTotalCreditNoteAmount(contract);
        if (m.size() <= 1) return result;

        double totalAmountReceivedPayments = (double) m.get("totalAmountOfSDR") +
                (double) m.get("totalAmountOfMonthlyPayments") +
                (double) m.get("totalAmountOfPreCollected");

        boolean creditNoteCond = (double) m.get("oldPrice") <= totalAmountReceivedPayments;
        boolean isFilipinoMaid = contract.getHousemaid().getNationality().getCode()
                .equals(AccountingModule.PICKLIST_ITEM_NATIONALITY_FILIPINO_1);

        result.put(isFilipinoMaid ? "filipinaCreditNoteCond" : "nonFilipinaCreditNoteCond", String.valueOf(creditNoteCond));

        return result;
    }
}