package com.magnamedia.service;


import com.google.api.client.util.ArrayMap;
import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.exception.BusinessException;
import com.magnamedia.core.repository.PicklistItemRepository;
import com.magnamedia.entity.*;
import com.magnamedia.entity.ContractPaymentTermDetails.*;
import com.magnamedia.extra.DiscountsWithVatHelper;
import com.magnamedia.extra.PaymentHelper;
import com.magnamedia.extra.Utils;
import com.magnamedia.extra.VatInfoWrapper;
import com.magnamedia.helper.ContractPaymentTermHelper;
import com.magnamedia.helper.PicklistHelper;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.PaymentStatus;
import com.magnamedia.repository.ContractPaymentRepository;
import com.magnamedia.repository.NewVisaRequestExpenseRepository;
import com.magnamedia.repository.PaymentRepository;
import org.joda.time.DateTime;
import org.joda.time.Days;
import org.joda.time.LocalDate;
import org.joda.time.Months;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Osman
 * @created 30/01/2025 - 2:30 PM
 * ACC-8721
 */

@Service
public class CalculateDiscountsWithVatService {
    private final static Logger logger = Logger.getLogger(CalculateDiscountsWithVatService.class.getName());


    /** ///////// Discount Start Date In Millis  ///////////////
   //////////// Additional Discount End Date In Millis //// **/
    public boolean isDuringPremiumPeriod(ContractPaymentTerm cpt, Date date) {

        Date startDiscountDate = new Date(getDiscountStartDateInMillis(
                cpt.getContract().getStartOfContract(),
                cpt.getContract().getIsProRated(), cpt));
        return date.before(startDiscountDate);
    }

    public long getDiscountStartDateInMillis(ContractPaymentTerm contractPaymentTerm) {
        return getDiscountStartDateInMillis(contractPaymentTerm.getContract().getStartOfContract(),
                contractPaymentTerm.isIsProRated(), contractPaymentTerm);
    }

    public long getDiscountStartDateInMillis(Date startDate, boolean isProRated, AbstractPaymentTerm paymentTerm) {
        return getDiscountStartDateInMillis(startDate, isProRated, paymentTerm, paymentTerm.getDiscountEffectiveAfter());
    }

    public long getDiscountStartDateInMillis(Date startDate, boolean isProRated, AbstractPaymentTerm paymentTerm, int numOfMonths) {

        DateTime contractStartDate = new DateTime(startDate);

        // ACC-1689 if have been switch from non Philippine to Philippine before
        if (paymentTerm instanceof ContractPaymentTerm) {
            ContractPaymentTerm cpt = ((ContractPaymentTerm) paymentTerm).getSwitchingToPhilipino() ?
                    (ContractPaymentTerm) paymentTerm :
                    ((ContractPaymentTerm) paymentTerm).getContract()
                            .hasBeenUpgradedNationalityBefore((ContractPaymentTerm) paymentTerm);

            if (cpt != null) {
                contractStartDate = new DateTime(cpt.getSwitchOrReplaceNationalityDate());
                isProRated = true;
            }

            boolean isMv = ((ContractPaymentTerm) paymentTerm).getContract().isMaidVisa();
            // ACC-2077
            if (isMv && contractStartDate.getDayOfMonth() ==
                    contractStartDate.dayOfMonth().withMaximumValue().getDayOfMonth()) {

                numOfMonths++;
            }
        }

        if (isProRated) numOfMonths += 1;

        DateTime startDiscountDate = contractStartDate.plusMonths(numOfMonths)
                .withDayOfMonth(1).withTimeAtStartOfDay();
        logger.info("startDiscountDate: " + startDiscountDate.toString("yyyy-MM-dd HH:mm:ss"));

        return startDiscountDate.toDate().getTime();
    }

    public Date getAdditionalDiscountEndDateInMillis(ContractPaymentTerm cpt) {
        return getAdditionalDiscountEndDateInMillis(cpt.getContract().getStartOfContract(), cpt.isIsProRated(), cpt);
    }

    public Date getAdditionalDiscountEndDateInMillis(Date startDate, boolean isProRated, ContractPaymentTerm paymentTerm) {
        Date discountDate = new Date(getDiscountStartDateInMillis(startDate, isProRated, paymentTerm));

        int shift = paymentTerm.getAdditionalDiscountMonths() != null ?
                paymentTerm.getAdditionalDiscountMonths() - paymentTerm.getDiscountEffectiveAfter() :
                0;

        return shift >= 0 ? new DateTime(discountDate).plusMonths(shift).toDate() :
                new DateTime(discountDate).minusMonths(-shift).toDate();
    }

    public Date getContractPaymentTermDetailsDiscountEndDate(ContractPaymentTermDetails d, ContractPaymentType t) {
        if (d.getEndDate() != null) return d.getEndDate();

        ContractPaymentRepository contractPaymentRepository = Setup.getRepository(ContractPaymentRepository.class);
        int discountedPayments = contractPaymentRepository.countContractPaymentTermDetailsDiscountedPayment(
                t.getContractPaymentTerm().getContract(), t.getType());
        int remainingMonths = t.getContractPaymentTerm().getCreditNoteMonths() - discountedPayments;

        if (remainingMonths <= 0) {
            logger.info("discountedPayments: " + discountedPayments + "; remainingMonths: " + remainingMonths);
            return null;
        }

        int receivedPayments = contractPaymentRepository.countReceivedPaymentWithoutProrated(
                t.getContractPaymentTerm().getContract(), t.getType());

        LocalDate newStartsDate = new LocalDate(d.getStartDate());
        if (receivedPayments > 0) {
            newStartsDate = newStartsDate.plusMonths(receivedPayments * (t.getRecurrence() == null ? 1 : t.getRecurrence()));
        }

        logger.info("receivedPayments: " + receivedPayments +
                "; oldStartDate: " + new LocalDate(d.getStartDate()).toString("yyyy-MM-dd") +
                "; newStartsDate: " + newStartsDate.toString("yyyy-MM-dd"));

        LocalDate endDate = newStartsDate
                .plusMonths((remainingMonths - 1) * (t.getRecurrence() == null ? 1 : t.getRecurrence()))
                .dayOfMonth()
                .withMaximumValue();

        if (t.getContractPaymentTerm().getContract().isOneMonthAgreement() && (d.isProRatedPassed() ||
                Setup.getRepository(PaymentRepository.class)
                        .existsMonthlyPaymentProRatedNotDeleted(
                        t.getContractPaymentTerm().getContract(), endDate.toDate()))) {
            endDate = endDate.plusMonths(1);
            logger.info("endDate after add prorated month: " + endDate.toString("yyyy-MM-dd"));
        }

        logger.info("endDate " + endDate.toString("yyyy-MM-dd"));
        return endDate.toDate();
    }


    /** ///////// Update Payment: 1- Add Normal Discount and Addon on amount //////////
   ////////// ///////////////////////////////////////////////////////////// ////// **/
    public ContractPayment updatePaymentAmountWithDiscountAndAddonAndDetails(ContractPaymentTerm cpt, ContractPayment cp, boolean finalAmount) {

        return updatePaymentAmountWithDiscountAndAddonAndDetails(cpt, cp, getDiscountStartDateInMillis(cpt), finalAmount);
    }

    public ContractPayment updatePaymentAmountWithDiscountAndAddonAndDetails(
            ContractPaymentTerm cpt, ContractPayment cp,
            long discountStartDateInMillis, boolean finalAmount) {

        // 1- Update Amount And Discount
        double discountAmount = getDiscountPayment(cpt, cp.getPaymentType(), cp.getDate(), new LocalDate(discountStartDateInMillis));
        cp.setDiscountAmount(discountAmount);
        if (!finalAmount) { cp.setAmount(cp.getAmount() - discountAmount); }

        // 2- Update Amount With AddOn
        cp.setAmount(getAmountAfterApplyAddOnPaymentType(cpt, cp.getPaymentType(), cp.getDate(), cp.getAmount()));

        // 3- Update Amount ContractPaymentTermDetails Via ContractPayment
        updateAmountContractPaymentTermDetailsViaContractPayment(cpt, cp);

        return cp;
    }

    public void updateAmountContractPaymentTermDetailsViaContractPayment(ContractPaymentTerm cpt, ContractPayment cp) {
        if (cp.getMoreAdditionalDiscount() != 0.0 || cp.isAddedManuallyFromClientProfile()) return;

        if (cp.getIsProRated() && !cp.getProRatedPlusMonth()) {
            cpt.getContractPaymentTermDetails().forEach(d -> {
                logger.info("proRatedPassed");
                d.setProRatedPassed(true);
            });
            return;
        }

        Map<String, Object> m = updateDetailsAndGetAmountAfterApplyCreditNoteDiscount(cpt, cp.getDate(), cp.getAmount(), cp.getPaymentType());

        cp.setAmount((Double) m.get("amount"));
        cp.setMoreAdditionalDiscount((Double) m.getOrDefault("moreAdditionalDiscount", 0.0));
    }

    public boolean updatePaymentsAdditionalDiscountAndAmount(
            Integer additionalDiscountedPaymentsCount,
            Double additionalDiscountAmount,
            List<ContractPayment> payments,
            Double sumAdditionalDiscount) {

        logger.info("additionalDiscountAmount: " + additionalDiscountAmount +
                "; additionalDiscountedPaymentsCount: " + additionalDiscountedPaymentsCount +
                "; sumAdditionalDiscount: " + sumAdditionalDiscount);

        if (additionalDiscountAmount == null || additionalDiscountAmount == 0 ||
                additionalDiscountedPaymentsCount == null || additionalDiscountedPaymentsCount <= 0)
            return false;

        // double additionalDiscount, full monthly payments
        ContractPaymentTermHelper contractPaymentTermHelper = Setup.getApplicationContext().getBean(ContractPaymentTermHelper.class);
        PicklistItem monthlyPaymentType = contractPaymentTermHelper.getItem("TypeOfPayment", AbstractPaymentTypeConfig.MONTHLY_PAYMENT_TYPE_CODE);
        PicklistItem serviceChargePaymentType = contractPaymentTermHelper.getItem("TypeOfPayment", "service_charge");
        double vat = DiscountsWithVatHelper.getVatPercent();

        Integer paymentsGotAdditionalDiscount = 0;
        boolean additionalDiscountAdded = false;

        for (ContractPayment payment : payments) {
            if ((payment.getIsProRated() && !payment.getProRatedPlusMonth()) ||
                    (payment.getPaymentType().getId().longValue() != monthlyPaymentType.getId().longValue() &&
                            payment.getPaymentType().getId().longValue() != serviceChargePaymentType.getId().longValue()))
                continue;

            // check if additional discount is consumed
            if (sumAdditionalDiscount >= additionalDiscountAmount ||
                    (paymentsGotAdditionalDiscount.equals(additionalDiscountedPaymentsCount))) {
                return additionalDiscountAdded;
            }

            // set additional discount amount
            payment.setAdditionalDiscountAmount(Math.floor(additionalDiscountAmount / additionalDiscountedPaymentsCount));
            sumAdditionalDiscount += payment.getAdditionalDiscountAmount();
            Double restOfDiscount = additionalDiscountAmount - sumAdditionalDiscount;

            if (restOfDiscount > 0 && restOfDiscount < payment.getAdditionalDiscountAmount()) {
                payment.setAdditionalDiscountAmount(payment.getAdditionalDiscountAmount() + restOfDiscount);
                sumAdditionalDiscount += restOfDiscount;
            }

            // set amount
            payment.setAmount(Math.floor(DiscountsWithVatHelper.getFinalAmountAfterAddDiscount(
                    payment.getAmount(), payment.getAdditionalDiscountAmount(), vat)));


            paymentsGotAdditionalDiscount++;
            logger.info("paymentsGotAdditionalDiscount: " + paymentsGotAdditionalDiscount);

            additionalDiscountAdded = true;
        }

        return additionalDiscountAdded;
    }

    /** ///////// Update Payment: 2- Amount Contract Payment By Contract Payment Type /////////
   ////////// ///////////////////////////////////////////////////////////////////// ////// **/
    public Map<String, Object> getContractPaymentAmount(DateTime cpDate, ContractPaymentType t) {
        Map<String, Object> map = new HashMap<>();
        Double amount = t.getAmount();

        if (!isDuringPremiumPeriod(t.getContractPaymentTerm(), cpDate.toDate()) && t.getDiscount() != null) {
            amount -= t.getDiscount();
            map.put("discount", t.getDiscount());
        }

        // Additional Discount
        Double additionalDiscountAmount = t.getAffectedByAdditionalDiscount() && t.getAdditionalDiscountedPaymentsCount() > 0 ?
                t.getAdditionalDiscountAmountPerPayment() : 0D;
        if (additionalDiscountAmount != 0) {
            amount = Math.floor(DiscountsWithVatHelper.getFinalAmountAfterAddDiscount(amount, additionalDiscountAmount));
            t.setAdditionalDiscountedPaymentsCount(t.getAdditionalDiscountedPaymentsCount() - 1);
        }
        map.put("additionalDiscountAmount", additionalDiscountAmount);

        // Credit note
        Double moreAdditionalDiscount = updateContractPaymentTermDetailsAndGetCreditNote(t.getContractPaymentTerm(), cpDate.toDate(), t.getType());
        map.putAll(getAmountAfterApplyCreditNoteDiscount(moreAdditionalDiscount, amount));

        logger.info(map.entrySet().toString());
        return map;
    }

    /** ///////// Get Amount Of Monthly Payment //////////
   //////////// ////////////////////////////// ///////**/
    public Double getCPTAmountAtTime(ContractPaymentTerm cpt, Date date) {
        return isDuringPremiumPeriod(cpt, date) ?
                getMonthlyPayment(cpt, date) :
                getDiscountedMonthlyPayment(cpt, date);
    }

    public double getDiscountedMonthlyPayment(ContractPaymentTerm cpt, Date dateOfPayment) {
        return getMonthlyPayment(cpt, dateOfPayment) - cpt.getDiscount();
    }

    public double getMonthlyPayment(ContractPaymentTerm cpt, Date dateOfPayment) {

        return cpt.getMonthlyPaymentType() != null ?
                getAmountAfterApplyAddOnPaymentType(
                        cpt, cpt.getMonthlyPaymentType().getType(),
                        dateOfPayment, cpt.getMonthlyPaymentType().getAmount()) :
                0D;
    }

    /** ///////// Get Amount Of Monthly Payment With Discounts //////////
   ///////////// //////////////////////////////////////////// ///////**/
    public Map<String, Object> getAmountOfMonthlyPaymentAtTimeWithDiscounts(ContractPaymentTerm cpt, LocalDate d) {
        return getAmountOfMonthlyPaymentAtTimeWithDiscounts(cpt, d, false);
    }

    public Map<String, Object> getAmountOfMonthlyPaymentAtTimeWithDiscounts(ContractPaymentTerm cpt, LocalDate d, boolean useDateWithOMA) {
        return getAmountOfMonthlyPaymentAtTimeWithDiscounts(cpt, d, useDateWithOMA, new HashMap<>());
    }

    public Map<String, Object> getAmountOfMonthlyPaymentAtTimeWithDiscounts(ContractPaymentTerm cpt, LocalDate date, boolean useDateWithOMA, Map<String, Object> map) {

        OneMonthAgreementFlowService oneMonthAgreementFlowService = Setup.getApplicationContext().getBean(OneMonthAgreementFlowService.class);
        if (oneMonthAgreementFlowService.isPayingViaCreditCard(cpt.getContract())) {
            date = useDateWithOMA ? date : oneMonthAgreementFlowService.getCurrentPaymentDate(cpt.getContract()).toLocalDate();
            return getAmountOfMonthlyPaymentAtTimeBeforeProratedForOMA(cpt, date.toDate());
        }

        Map<String, Object> m = new HashMap<>();
        boolean premium = isDuringPremiumPeriod(cpt, date.toDate());

        // 1- CPT Amount + Discount + Addon
        double monthlyPaymentAmount = premium ? getMonthlyPayment(cpt, date.toDate()) : getDiscountedMonthlyPayment(cpt, date.toDate());
        m.put("amount", monthlyPaymentAmount);
        m.put("affectedByAdditionalDiscount", false);
        m.put("additionalDiscountAmountPerPayment", 0.0D);

        boolean includeWorkerSalary = !premium && cpt.getContract().isMaidVisa();
        m.put("includeWorkerSalary", includeWorkerSalary);
        m.put("workerSalary", includeWorkerSalary && cpt.getContract().getWorkerSalaryWithoutVat() != null ? cpt.getContract().getWorkerSalaryNew() : 0D);
        m.put("discountAmount", includeWorkerSalary ? cpt.getDiscount() : 0D);

        // 2- Check if Prorated Payment
        if (cpt.getContract() != null && cpt.getContract().getIsProRated() &&
                new LocalDate(cpt.getContract().getStartOfContract()).toString("yyyy-MM")
                        .equals(date.toString("yyyy-MM"))) {
            logger.info("prorated payment");
            map.put("dailyRateAmount", cpt.getDailyRateAmount());
            map.put("monthlyPaymentAmount", cpt.getMonthlyPayment());
            map.put("proRatedDate", date.withDayOfMonth(
                    new LocalDate(cpt.getContract().getStartOfContract()).getDayOfMonth()));
            map.put("isOneMonthAgreement", cpt.getContract().isOneMonthAgreement());
            map.put("firstMonthPayment", cpt.getFirstMonthPayment());
            m.put("amount", getProRatedAmount(map));
            return m;
        }

        logger.info("Date: " + date.toString("yyyy-MM-dd") + " ; amount: " + m.get("amount"));

        // Apply Additional discount and Credit note discount
        m.putAll(applyAdditionalDiscountAndCreditNoteOnAmount(cpt, date, (Double) m.get("amount")));

        logger.info("Final Amount : " + m.get("amount"));
        return m;
    }

    public Map<String, Object> getAmountOfMonthlyPaymentAtTimeBeforeProratedForOMA(
            ContractPaymentTerm cpt,
            Date date) {

        logger.info("cpt id : " + cpt.getId() + "; date : " + date);

        AbstractPaymentTypeConfig monthlyPaymentType = cpt.getMonthlyPaymentType();
        Map<String, Object> m = new HashMap<>();
        m.put("amount", monthlyPaymentType.getAmount());
        m.put("affectedByAdditionalDiscount", false);
        m.put("includeWorkerSalary", false);

        // Apply Additional discount and Credit note discount
        m.putAll(applyAdditionalDiscountAndCreditNoteOnAmountOMA(cpt, date, (Double) m.get("amount"), monthlyPaymentType));

        logger.info("Final Amount : " + m.get("amount"));
        return m;
    }

    /** ///////// Apply Additional Discount And Credit Note On Amount ////////// **/
    // ACC-4905
    public double getProRatedAmount(Map<String, Object> map) {

        Double dailyRateAmount = (Double) map.get("dailyRateAmount");
        Double monthlyPaymentAmount = (Double) map.get("monthlyPaymentAmount");
        LocalDate proRatedDate = (LocalDate) map.get("proRatedDate");
        // ACC-5712
        Boolean isOneMonthAgreement = (Boolean) map.get("isOneMonthAgreement");

        if (!isOneMonthAgreement && !(boolean) map.getOrDefault("ignoreFirstMonthPayment", false) && map.get("firstMonthPayment") != null) {
            logger.info("firstMonthPayment: " + map.get("firstMonthPayment"));
            return (double) map.get("firstMonthPayment");
        }

        double dailyAmount = !isOneMonthAgreement && dailyRateAmount != null && dailyRateAmount > 0.0
                ? dailyRateAmount
                : (monthlyPaymentAmount / proRatedDate.dayOfMonth().withMaximumValue().getDayOfMonth());
        int remainingDays =
                Days.daysBetween(proRatedDate, proRatedDate.dayOfMonth().withMaximumValue()).getDays() + 1;
        logger.info("remainingDays: " + remainingDays + "; dailyAmount: " + dailyAmount);

        return (double) Math.round(remainingDays * dailyAmount);
    }

    public double getProRatedAmount(ContractPaymentTerm cpt, LocalDate d) {

        Map<String, Object> map = new ArrayMap<>();
        map.put("dailyRateAmount", cpt.getDailyRateAmount());
        map.put("monthlyPaymentAmount", cpt.getMonthlyPayment());
        map.put("proRatedDate", d);
        map.put("isOneMonthAgreement", cpt.getContract().isOneMonthAgreement());
        map.put("firstMonthPayment", cpt.getFirstMonthPayment());

        return getProRatedAmount(map);
    }

    /** ///////// Apply Additional Discount And Credit Note On Amount //////////
    ////////// ////////////////////////////////////////////////////// ///// **/
    public Map<String, Object> applyAdditionalDiscountAndCreditNoteOnAmount(ContractPaymentTerm cpt, LocalDate date, double amount ) {

        // 1- Additional discount
        Double additionalDiscount = getAdditionalDiscountPerPayment(cpt, date);
        Map<String, Object> m = getPaymentAmountAndAdditionalDiscount(additionalDiscount, amount);

        // 2- Credit note
        Double moreAdditionalDiscount = updateContractPaymentTermDetailsAndGetCreditNote(cpt, date.toDate(), cpt.getMonthlyPaymentType().getType());
        m.putAll(getAmountAfterApplyCreditNoteDiscount(moreAdditionalDiscount, (Double) m.get("amount")));

        return m;
    }

    public Map<String, Object> applyAdditionalDiscountAndCreditNoteOnAmountOMA(
            ContractPaymentTerm cpt, Date date,
            double amount, AbstractPaymentTypeConfig monthlyPaymentType) {

        if (!cpt.getContract().getIsOneMonthAgreement()) { new HashMap<>(); }

        // 1- Additional discount
        DateTime paymentStartOnDate = new DateTime(cpt.getContract().getStartOfContract()).plusMonths(monthlyPaymentType.getStartsOn());
        int numberOfPayment = Months.monthsBetween(paymentStartOnDate,
                new DateTime(date).dayOfMonth().withMaximumValue()).getMonths();

        Double additionalDiscount = getAdditionalDiscountPerPaymentOMA(cpt, numberOfPayment);
        Map<String, Object> m = getPaymentAmountAndAdditionalDiscount(additionalDiscount, amount);

        // 2- Credit note discount
        Double moreAdditionalDiscount = updateContractPaymentTermDetailsAndGetCreditNote(cpt, date, monthlyPaymentType.getType());
        m.putAll(getAmountAfterApplyCreditNoteDiscount(moreAdditionalDiscount, (Double) m.get("amount")));

        return m;
    }


    /** ////////////////////////////////////
   //////////// All Discounts /////////////
  ////////////////////////////////////////


   ////////// Get Normal Discount and AddOn ////////////
  ////////// /////////////////////////////// ////// **/
    public double getDiscountPayment(ContractPaymentTerm cpt, PicklistItem paymentType, Date paymentDate, LocalDate discountStartDate) {

        //ACC-4808
        if (cpt.getDiscount() == 0) return 0D;
        if (!PaymentHelper.isMonthlyPayment(paymentType)) return 0D;
        if (paymentDate.before(discountStartDate.toDateTimeAtStartOfDay().toDate())) return 0D;

        return cpt.getDiscount();
    }

    public Double getAmountAfterApplyAddOnPaymentType(ContractPaymentTerm cpt, PicklistItem paymentType, Date date, double amount) {

        return amount + getAddOnPaymentTypeAmount(cpt, paymentType, date, amount);
    }

    public double getAddOnPaymentTypeAmount(ContractPaymentTerm cpt, PicklistItem paymentType, Date paymentDate, Double paymentAmount) {

        if (!PaymentHelper.isMonthlyPayment(paymentType) || !cpt.getContract().isMaidVisa()) {
            return 0D;
        }

        return getAddOnPaymentTypeAmount(cpt, paymentDate, paymentAmount);
    }

    private double getAddOnPaymentTypeAmount(ContractPaymentTerm cpt, Date paymentDate, Double paymentAmount) {

        if (paymentDate == null || paymentAmount == null || cpt.getContractPaymentTypes() == null) {

            return 0D;
        }

        // 2- Update Amount with AddOnPayment
        ContractPaymentType t = DiscountsWithVatHelper.getAddOnPaymentType(paymentDate, cpt.getContractPaymentTypes(), cpt.getContract());
        double addOnAmount = t != null ? t.getAmount() : 0D;

        if (addOnAmount != 0.0) {
            logger.info("cpt id: " + cpt.getId() +
                    ";payment date : " + paymentDate +
                    "; payment amount : " + paymentAmount +
                    "; add On Amount" + addOnAmount);
        }

        return addOnAmount;
    }

    /** //////////// Get or Update Additional discount /////////////
    /////////////// ///////////////////////////////// ///////// **/
    public Map<String, Object> getPaymentAmountAndAdditionalDiscount(Double additionalDiscountAmountPerPayment, double amount) {

        Map<String, Object> m = new HashMap<>();
        m.put("amount", amount);

        if (additionalDiscountAmountPerPayment == null || additionalDiscountAmountPerPayment == 0.0) { return m; }

        amount = Math.floor(DiscountsWithVatHelper.getFinalAmountAfterAddDiscount(amount, additionalDiscountAmountPerPayment));

        m.put("amount", amount);
        m.put("additionalDiscountAmountPerPayment", additionalDiscountAmountPerPayment);
        m.put("affectedByAdditionalDiscount", true);

        logger.info("additionalDiscountAmountPerPayment: " + additionalDiscountAmountPerPayment + " ; amount: " + m.get("amount") );
        return m;
    }

    public Double getAdditionalDiscountPerPayment(ContractPaymentTerm cpt, LocalDate d) {
        return getAdditionalDiscountPerPayment(cpt, d, null);
    }

    public Double getAdditionalDiscountPerPaymentOMA(ContractPaymentTerm cpt, Integer numberOfPayment) {
        return getAdditionalDiscountPerPayment(cpt, null, numberOfPayment);
    }

    private Double getAdditionalDiscountPerPayment(ContractPaymentTerm cpt, LocalDate d, Integer numberOfPayment) {

        Integer additionalDiscountedPaymentsCount = cpt.getAdditionalDiscountMonthsCount(AbstractPaymentTypeConfig.MONTHLY_PAYMENT_TYPE_CODE);
        Double additionalDiscountAmount = cpt.getAdditionalDiscount();

        if (additionalDiscountAmount == null || additionalDiscountAmount <= 0 ||
                additionalDiscountedPaymentsCount == null || additionalDiscountedPaymentsCount <= 0) {
            return 0.0;
        }

        logger.info("cpt id: " + cpt.getId() +
                "; additionalDiscountAmount: " + additionalDiscountAmount +
                "; additionalDiscountedPaymentsCount: " + additionalDiscountedPaymentsCount);

        // Handle OMA
        if (cpt.getContract().getIsOneMonthAgreement()) {
            return numberOfPayment != null && numberOfPayment < additionalDiscountedPaymentsCount ?
                    additionalDiscountAmount / additionalDiscountedPaymentsCount : 0.0;
        }

        DateTime startDiscountDate = new DateTime(cpt.getContract().getStartOfContract())
                .plusMonths(cpt.getContract().getIsProRated() ? 1 : cpt.getContract().getProRatedPlusMonth() ? 2 : 0)
                .withDayOfMonth(1).withTimeAtStartOfDay();

        DateTime endDiscountDate = startDiscountDate.plusMonths(additionalDiscountedPaymentsCount - 1).dayOfMonth().withMaximumValue();
        logger.info("startDiscountDate: " + startDiscountDate + ", endDiscountDate: " + endDiscountDate + ", date: " + d);

        if (new DateTime(d.toDate()).plusMillis(1).isAfter(startDiscountDate) &&
                new DateTime(d.toDate()).minusMillis(1).isBefore(endDiscountDate)) {
            return additionalDiscountAmount / additionalDiscountedPaymentsCount;
        }
        return 0.0;
    }


    /** //////////// Get Credit Note Discount And Update ContractPaymentTermDetails /////////////
    ////////// /////////////////////////////////////////////////////////////////// ///////// **/
    public Map<String, Object> updateDetailsAndGetAmountAfterApplyCreditNoteDiscount(
            ContractPaymentTerm cpt, Date date,
            double amount, PicklistItem PaymentType) {

        return getAmountAfterApplyCreditNoteDiscount(
                updateContractPaymentTermDetailsAndGetCreditNote(cpt, date, PaymentType),
                amount);
    }

    public Map<String, Object> getAmountAfterApplyCreditNoteDiscount(Double moreAdditionalDiscount, double amount) {

        Map<String, Object> m = new HashMap<>();
        m.put("amount", amount);
        m.put("moreAdditionalDiscount", 0D);

        if (moreAdditionalDiscount == null || moreAdditionalDiscount == 0D) { return m; }

        amount = DiscountsWithVatHelper.getFinalAmountAfterAddDiscount(amount, moreAdditionalDiscount);
        logger.info("moreAdditionalDiscount: " + moreAdditionalDiscount + " ; amount: " + amount);

        m.put("amount", (double) Math.round(amount));
        m.put("moreAdditionalDiscount", Math.floor(moreAdditionalDiscount));
        return m;
    }

    // ACC-6150 ACC-7151
    // Note: When returning Null, that means there were not any more additional discounts.
    private Double updateContractPaymentTermDetailsAndGetCreditNote(ContractPaymentTerm cpt, Date d, PicklistItem type) {

        if (cpt.getContractPaymentTermDetails().isEmpty()) return 0.0;


        cpt.setContractPaymentTermDetails(cpt.getContractPaymentTermDetails()
                .stream().filter(details -> !details.isFinished()).collect(Collectors.toList()));

        if (cpt.getContractPaymentTermDetails().isEmpty()) return 0.0;


        Double moreAdditionalDiscount = null;
        for (ContractPaymentTermDetails details : cpt.getContractPaymentTermDetails()) {

            switch (details.getDetailType()) {
                case DISCOUNT:
                    if (Source.CREDIT_NOTE.equals(details.getSource())) {
                        ContractPaymentType t = DiscountsWithVatHelper.getContractPaymentTypeWithAdditionalDiscount(cpt);
                        if (t == null || !t.getType().equals(type)) continue;

                        Date endDate = getContractPaymentTermDetailsDiscountEndDate(details, t);
                        if (endDate == null || d.getTime() > endDate.getTime()) {
                            details.setFinished(true);
                            continue;
                        }
                    }

                    double discount = details.getAmount() / details.getDiscountMonths();
                    moreAdditionalDiscount = moreAdditionalDiscount != null ? moreAdditionalDiscount + discount : discount;
                    logger.info("payment Date: " + new LocalDate(d).toString("yyyy-MM-dd") +
                            "; details id : " + details.getId() +
                            "; type: " + type.getName() +
                            "; Payment amount after updated " + moreAdditionalDiscount);
                    break;
            }
        }

        return moreAdditionalDiscount == null ? 0.0 : moreAdditionalDiscount;
    }


    public void validateAdditionalDiscount(Double additionalDiscount, Integer duration, Double monthlyPayment) {
        if (duration <= 0) {
            return;
        }

        double discountPerMonth = additionalDiscount / duration;

        double maxAllowedDiscount = Double.parseDouble(
                Setup.getParameter(Setup.getModule(AccountingModule.SALES_MODULE_CODE),
                        AccountingModule.PARAMETER_CONTRACT_MAX_DISCOUNT_ALLOWED)) *
                monthlyPayment;

        if (discountPerMonth > maxAllowedDiscount) {
            throw new RuntimeException("Additional Discount mustn't exceed Maximum allowed discount : " + maxAllowedDiscount);
        }
    }

    /** //////////// Update Vat Fields  /////////////
    //////////////// ////////////////// //////// **/
    public StringBuilder updateVatFields(ContractPayment cp) {
        StringBuilder log = new StringBuilder();
        updateVatFields(cp, log);
        return log;
    }

    public void updateVatFields(ContractPayment cp, StringBuilder log) {
        if (cp == null || cp.getConfirmed()) return;
        log.append("Contract Payment:").append(cp.getId() != null ? cp.getId() : "NULL").append(System.lineSeparator());

        PicklistItem type = Setup.getRepository(PicklistItemRepository.class).findOne(cp.getPaymentType().getId());
        cp.setPaymentType(type);
        cp.setVatPercent(DiscountsWithVatHelper.getVatPercent());

        double vat = 0D, visaFees = 0D;
        Contract contract = cp.getContractPaymentTerm().getContract();
        if (contract.isMaidCc()) {

            if (!type.hasTag(ContractPayment.NO_VAT_TAG) && contract.getClientPaidVat()) {
                vat = cp.getAmount() - DiscountsWithVatHelper.getAmountWithoutVat(cp.getAmount(), cp.getVatPercent());
            }

            cp.setVisaFeesWithoutVAT(0.0);
        } else {
            log.append("; includeWorkerSalary: ").append(cp.getIncludeWorkerSalary())
                    .append("; workerSalary: ").append(cp.getWorkerSalary()).append(System.lineSeparator());

            // 1- Worker Salary
            Double workerSalary = null;
            if (cp.getIncludeWorkerSalary()) {
                workerSalary = contract.getWorkerSalaryNew();
            }

            // 2- Visa Fees
            visaFees = cp.getAmount() - (workerSalary == null ? 0D : workerSalary);

            if (!type.hasTag(ContractPayment.NO_VAT_TAG) && contract.getClientPaidVat()) {
                // ACC-8721
                // 3- Worker Salary Vat
                if (workerSalary != null && contract.isWorkerSalaryVatted()) {
                    cp.setWorkerSalaryVAT(workerSalary - DiscountsWithVatHelper.getAmountWithoutVat(workerSalary, cp.getVatPercent()));
                    cp.setWorkerSalaryWithoutVAT(workerSalary - cp.getWorkerSalaryVAT());
                    log.append("WorkerSalaryVAT: ").append(cp.getWorkerSalaryVAT()).append(System.lineSeparator());
                }

                cp.setVisaFeesVAT(visaFees - DiscountsWithVatHelper.getAmountWithoutVat(visaFees, cp.getVatPercent()));
                cp.setVisaFeesWithoutVAT(visaFees - cp.getVisaFeesVAT());
                log.append("VisaFeesVAT: ").append(cp.getVisaFeesVAT());
            }

            vat = cp.getWorkerSalaryVAT() + cp.getVisaFeesVAT();
        }

        cp.setIncludeWorkerSalary(cp.getIncludeWorkerSalary());
        cp.setVat(vat);
        cp.setVisaFees(visaFees);
        cp.setPaymentWithoutVAT(cp.getAmount() - cp.getVat());
    }

    public void updateVatFields(Payment payment) {
        StringBuilder log = new StringBuilder();
        updateVatFields(payment, log);
        // logger.info(log.toString());
    }

    public void updateVatFields(Payment payment, StringBuilder log) {

        if (payment == null) { return; }
        log.append("Payment:").append(payment.getId() != null ? payment.getId() : "NULL");

        // CM-1315 (SUB CM-1324)
        // The Vat amount should be equal to the payment amount if the payment type is "Vat only refund"
        if (Arrays.asList("vat_only", "vat_only_refund")
                .contains(payment.getTypeOfPayment().getCode())) {

            payment.setIncludeWorkerSalary(false);
            payment.setVatPercent(1D);
            payment.setVat(payment.getAmountOfPayment());
            payment.setVisaFees(0D);
            payment.setPaymentWithoutVAT(0D);
            payment.setVisaFeesWithoutVAT(0D);
            return;
        }

        if (payment.getVatFilledFromFrontEnd() && (!payment.getVatPaidByClient() || (payment.getVat() != null && payment.getVat() != 0))) { return; }

        if (!payment.getVatPaidByClient()) {
            payment.setIncludeWorkerSalary(false);
            payment.setVatPercent(0D);
            payment.setVat(0D);
            payment.setVisaFees(0D);
            payment.setPaymentWithoutVAT(0D);
            payment.setVisaFeesWithoutVAT(0D);
            return;
        }

        if (payment.getId() == null && payment.getIncludeWorkerSalary() == null &&
                PaymentHelper.isMonthlyPayment(payment.getTypeOfPayment())) {
            payment.setIncludeWorkerSalary(!payment.getPaymentIsInitial());
        }

        if (payment.getIncludeWorkerSalary() == null) {
            payment.setIncludeWorkerSalary(false);
        }

        if (payment.getContract().getContractProspectType() == null) { throw new BusinessException("contract doesn't have Prospect Type"); }

        double visaFees = 0D, vat = 0D, visaFeesWithoutVAT = 0D;
        if (payment.getContract().isMaidCc()) {

            // 1- Add Vat
            payment.setVatPercent(getVatPercentValue(payment));
            if (payment.getTypeOfPayment().hasTag("refund")
                    || !payment.getTypeOfPayment().hasTag(ContractPayment.NO_VAT_TAG)) {

                vat = payment.getAmountOfPayment() - DiscountsWithVatHelper.getAmountWithoutVat(payment.getAmountOfPayment(), payment.getVatPercent());
            }
        } else {

            log.append("includeWorkerSalary: ").append(payment.getIncludeWorkerSalary())
                    .append("; workerSalary: ").append(payment.getWorkerSalary()).append(System.lineSeparator());
            // 1- Worker Salary
            if (!payment.getIncludeWorkerSalary()) {
                payment.setWorkerSalary(0D);
            } else if (payment.getWorkerSalary() == null) {
                payment.setWorkerSalary(payment.getContract().getWorkerSalaryNew());
            }

            // 2- Visa Fees
            payment.setVatPercent(getVatPercentValue(payment));
            visaFees = payment.getIncludeWorkerSalary() &&
                    Arrays.asList(
                            AbstractPaymentTypeConfig.MONTHLY_PAYMENT_TYPE_CODE,
                                    "paid_the_client_-_refund",
                                    "partial_mp_refunded_to_client")
                            .contains(payment.getTypeOfPayment().getCode()) ?
                    (payment.getAmountOfPayment() - payment.getWorkerSalary()) : payment.getAmountOfPayment();
            log.append("Visa Fees3: ").append(visaFees).append(System.lineSeparator());


            // 3- Add Vat
            if (payment.getTypeOfPayment().hasTag("refund") ||
                    !payment.getTypeOfPayment().hasTag(ContractPayment.NO_VAT_TAG)) {
                // ACC-8721
                // 4- Worker Salary Vat
                if (payment.getIncludeWorkerSalary() && payment.getContract().isWorkerSalaryVatted() && payment.getWorkerSalary() != null) {
                    payment.setWorkerSalaryVAT(payment.getWorkerSalary() - DiscountsWithVatHelper.getAmountWithoutVat(payment.getWorkerSalary(), payment.getVatPercent()));
                    payment.setWorkerSalaryWithoutVAT(payment.getWorkerSalary() - payment.getWorkerSalaryVAT());
                }

                payment.setVisaFeesVAT(visaFees - DiscountsWithVatHelper.getAmountWithoutVat(visaFees, payment.getVatPercent()));
                visaFeesWithoutVAT = visaFees - payment.getVisaFeesVAT();

                vat = payment.getWorkerSalaryVAT() + payment.getVisaFeesVAT();
            }
        }

        payment.setVisaFees(visaFees);
        payment.setVat(vat);
        payment.setPaymentWithoutVAT(payment.getAmountOfPayment() - payment.getVat());
        payment.setVisaFeesWithoutVAT(visaFeesWithoutVAT);

        checkPositiveFields(payment);
    }

    private double getVatPercentValue(Payment payment) {

        return Arrays.asList("paid_the_client_-_refund", "partial_mp_refunded_to_client", "non-mp-refund")
                .contains(payment.getTypeOfPayment().getCode()) ||
                !payment.getTypeOfPayment().hasTag(ContractPayment.NO_VAT_TAG) ?
                DiscountsWithVatHelper.getVatPercent() : 0D;
    }

    private void checkPositiveFields(Payment payment) {

        if (payment.getStatus() != null && payment.getStatus().equals(PaymentStatus.DELETED)) { return; }

        if (payment.getVat() < 0) { throw new BusinessException("vat must be positive value"); }
        if (payment.getVatPercent() < 0) { throw new BusinessException("vatPercent must be positive value"); }
        if (payment.getPaymentWithoutVAT() < 0) { throw new BusinessException("paymentWithoutVat must be positive value"); }
        if (payment.getVisaFeesWithoutVAT() < 0) { throw new BusinessException("visaFeesWithoutVAT must be positive value"); }
        if (payment.getVisaFees() < 0) { throw new BusinessException("visaFees must be positive value"); }
        if (payment.getWorkerSalaryWithoutVAT() < 0) { throw new BusinessException("workerSalaryWithoutVAT must be positive value"); }
    }

    public Double calculateVat(VatInfoWrapper vatInfoWrapper, Contract contract) {

        PicklistItem maidCc = PicklistHelper.getItem(AccountingModule.PICKLIST_PROSPECTTYPE, AccountingModule.MAID_CC_PROSPECT_TYPE_CODE);
        PicklistItem maidVisa = PicklistHelper.getItem(AccountingModule.PICKLIST_PROSPECTTYPE, AccountingModule.MAID_VISA_PROSPECT_TYPE_CODE);
        PicklistItem typeOfPayment = Setup.getRepository(PicklistItemRepository.class).findOne(vatInfoWrapper.getTypeOfPaymentId());

        Double vat = 0D;
        double amount = vatInfoWrapper.getAmountOfPayment();
        double vatPercent = Arrays.asList("paid_the_client_-_refund", "partial_mp_refunded_to_client", "non-mp-refund")
                .contains(typeOfPayment.getCode()) || !typeOfPayment.hasTag(ContractPayment.NO_VAT_TAG) ?
                DiscountsWithVatHelper.getVatPercent() : 0D;

        if (vatInfoWrapper.getProspectTypeId().equals(maidCc.getId())) {

            if (typeOfPayment.hasTag("refund") || !typeOfPayment.hasTag(ContractPayment.NO_VAT_TAG)) {
                vat = Utils.roundDownMode(amount - DiscountsWithVatHelper.getAmountWithoutVat(amount, vatPercent), 1);
            }

        } else if (vatInfoWrapper.getProspectTypeId().equals(maidVisa.getId())) {

            double visaFees = vatInfoWrapper.getIncludeWorkerSalary()
                    && Arrays.asList(
                            AbstractPaymentTypeConfig.MONTHLY_PAYMENT_TYPE_CODE,
                            "partial_mp_refunded_to_client",
                            "paid_the_client_-_refund")
                    .contains(typeOfPayment.getCode()) ?
                    (amount - (vatInfoWrapper.getWorkerSalary() != null ? vatInfoWrapper.getWorkerSalary() : 0D)) : amount;

            if (typeOfPayment.hasTag("refund")
                    || !typeOfPayment.hasTag(ContractPayment.NO_VAT_TAG)) {

                vat = Utils.roundDownMode(visaFees - DiscountsWithVatHelper.getAmountWithoutVat(visaFees, vatPercent), 1);

                // ACC-8721
                // 4- Worker Salary Vat
                if (vatInfoWrapper.getIncludeWorkerSalary() && contract.isWorkerSalaryVatted() && vatInfoWrapper.getWorkerSalary() != null) {
                    double workerSalaryVat = Utils.roundDownMode(
                            vatInfoWrapper.getWorkerSalary() -
                                    DiscountsWithVatHelper.getAmountWithoutVat(vatInfoWrapper.getWorkerSalary(), vatPercent),
                            1);

                    vat += workerSalaryVat;
                }
            }
        }
        return vat;
    }

    public Map<String, Object> getPaymentDiscountInfo(
            Contract contract, PicklistItem paymentType,
            Date date, boolean isProRated, boolean proRatedAdded) {

        if (date == null) date = new Date();

        Map<String, Object> r = new HashMap<>();
        r.put("vatPercent", DiscountsWithVatHelper.getVatPercent());
        r.put("isVATTED", !paymentType.hasTag(ContractPayment.NO_VAT_TAG));

        if (paymentType.getCode().equals("overstay_fee")) {
            r.put("amount", 0.0D);
            if(contract.getHousemaid() != null){
                List<Double> amounts = Setup.getRepository(NewVisaRequestExpenseRepository.class)
                        .findOverstayFeeByHousemaidAndStatusPending(contract.getHousemaid());
                if (!amounts.isEmpty() && amounts.get(0) != null) {
                    r.put("amount", amounts.get(0));
                }
            }
            return r;
        }

        ContractPaymentTerm cpt = contract.getActiveContractPaymentTerm();
        ContractPaymentType type = cpt.getContractPaymentTypes()
                .stream().filter(t -> t.getType().getCode().equals(paymentType.getCode()))
                .findFirst().orElse(null);

        r.put("amount", 0.0D);
        r.put("additionalDiscount", 0.0D);
        r.put("moreAdditionalDiscount", 0.0D);
        if (type == null) return r;

        r.put("includeWorkerSalary", !isDuringPremiumPeriod(cpt, date) && cpt.getContract().isMaidVisa() &&
                PaymentHelper.isMonthlyPayment(type.getType()));
        r.put("workerSalary", ((boolean) r.get("includeWorkerSalary")) && cpt.getContract().getWorkerSalaryWithoutVat() != null ? cpt.getContract().getWorkerSalaryNew() : 0D);
        r.put("discountAmount", ((boolean) r.get("includeWorkerSalary")) ? cpt.getDiscount() : 0D);
        r.put("amount", type.getType().getCode().equals("monthly_payment") ?
                getCPTAmountAtTime(cpt, date) :
                type.getAmount());

        if (type.getType().getCode().equals("monthly_payment")) {
            if (isProRated) {
                r.put("amount", getProRatedAmount(cpt, new LocalDate(date)));
                return r;
            }

            if (contract.getWaivedMonths() > 0 && contract.isMaidVisa() &&
                    new LocalDate(date)
                            .isBefore(new LocalDate(contract.getStartOfContract())
                                    .plusMonths(contract.getWaivedMonths()).dayOfMonth().withMinimumValue())) {
                r.put("amount", 0.0D);
                return r;
            }

            if (!type.getAffectedByAdditionalDiscount()) return r;

            if (contract.isOneMonthAgreement() && proRatedAdded) {
                cpt.getContractPaymentTermDetails().forEach(d -> {
                    logger.info("proRatedPassed");
                    d.setProRatedPassed(true);
                });
            }

            Map<String, Object> m = getAmountOfMonthlyPaymentAtTimeWithDiscounts(cpt, new LocalDate(date));
            r.put("additionalDiscount", m.getOrDefault("additionalDiscountAmountPerPayment", 0.0D));
            r.put("moreAdditionalDiscount", m.getOrDefault("moreAdditionalDiscount", 0.0D));

            logger.info("response: " + r.keySet());
            return r;
        }

        if (!type.getAffectedByAdditionalDiscount()) return r;

        LocalDate startDate = new LocalDate(contract.getStartOfContract())
                .plusMonths(type.getStartsOn() + (contract.getIsProRated() ? 1 : 0))
                .dayOfMonth().withMinimumValue();

        // Calculate additionalDiscount
        if(cpt.getAdditionalDiscount() != null && cpt.getAdditionalDiscountMonths() != null) {
            if (new LocalDate(date).toString("yyyy-MM").equals(startDate.toString("yyyy-MM"))) {
                r.put("additionalDiscount", cpt.getAdditionalDiscount());
            }
        }

        // Calculate moreAdditionalDiscount
        if(cpt.getCreditNote() != null && cpt.getCreditNoteMonths() != null) {
            if (new LocalDate(date).toString("yyyy-MM").equals(startDate.toString("yyyy-MM"))) {
                r.put("moreAdditionalDiscount", cpt.getCreditNote());
            }
        }

        logger.info("response: " + r.keySet());
        return r;
    }
}



