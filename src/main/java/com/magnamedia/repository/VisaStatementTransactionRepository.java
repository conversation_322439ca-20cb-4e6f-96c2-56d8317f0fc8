package com.magnamedia.repository;

import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.VisaStatement;
import com.magnamedia.entity.VisaStatementTransaction;
import com.magnamedia.extra.VisaStatementTransactionType;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface VisaStatementTransactionRepository extends BaseRepository<VisaStatementTransaction> {
    VisaStatementTransaction findTopByStatementAndFinished(VisaStatement statement, Boolean finished);
    List<VisaStatementTransaction> findByStatement(VisaStatement statement);

    VisaStatementTransaction findTopByStatementAndTypeNotAndFinishedFalse(
            VisaStatement statement, VisaStatementTransactionType t);
}