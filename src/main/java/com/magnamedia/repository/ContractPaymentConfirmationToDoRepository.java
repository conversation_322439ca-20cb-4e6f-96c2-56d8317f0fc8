package com.magnamedia.repository;

import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.Contract;
import com.magnamedia.entity.ContractPaymentConfirmationToDo;
import com.magnamedia.entity.ContractPaymentConfirmationToDo.Source;
import com.magnamedia.entity.ContractPaymentTerm;
import com.magnamedia.entity.workflow.FlowEventConfig;
import com.magnamedia.entity.workflow.FlowSubEventConfig;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Created by Mamon.Masof on 7/4/2021.
 */

@Repository
public interface ContractPaymentConfirmationToDoRepository extends BaseRepository<ContractPaymentConfirmationToDo> {
    
    boolean existsByContractPaymentTerm_ContractAndConfirmedAndShowOnERP(Contract c, boolean conformed, boolean showERP);

    @Query("select t " +
            "from ContractPaymentConfirmationToDo t " +
            "where t.paymentMethod = 'CARD' and t.confirmed = 0 and t.showOnERP = 0 and t.disabled = 0 and t.source <> 'CLIENT_REFUND' and " +
                "t.payTabsResponseMessage is null and t.creditCardInfo is not null and " +
                "not exists (select 1 from EPaymentTransaction e where e.relatedEntityId = t.id and e.relatedEntityType = 'ContractPaymentConfirmationToDo')")
    List<ContractPaymentConfirmationToDo> findForPayTabTransactionJob();

    ContractPaymentConfirmationToDo findFirstByContractPaymentTerm_ContractAndSourceAndShowOnERPFalseAndDisabledFalseOrderByCreationDateDesc(
            Contract contract, Source source);

    ContractPaymentConfirmationToDo findFirstByContractPaymentTerm_ContractAndSourceAndShowOnERPFalseAndDisabledFalseAndCreationDateGreaterThan(
            Contract contract, Source source, Date dt);

    ContractPaymentConfirmationToDo findFirstByContractPaymentTerm_ContractAndSourceInAndShowOnERPFalseAndDisabledFalseOrderByCreationDateDesc(
            Contract contract, List<Source> source);

    List<ContractPaymentConfirmationToDo> findByContractPaymentTerm_ContractAndSourceInAndShowOnERPFalseAndDisabledFalse(
            Contract contract, List<Source> source);

    List<ContractPaymentConfirmationToDo> findByContractPaymentTerm_ContractAndSourceAndDisabledFalse(
            Contract contract, Source source);

    List<ContractPaymentConfirmationToDo> findByContractPaymentTerm_ContractAndSourceAndDisabledFalseAndPurpose(
            Contract contract, Source source, ContractPaymentConfirmationToDo.Purpose purpose);

    @Query("select distinct todo from ContractPaymentWrapper cpw " +
            "join cpw.contractPaymentConfirmationToDo todo " +
            "where todo.contractPaymentTerm = ?1 and todo.source = ?2 and todo.disabled = 0 and todo.showOnERP = 0 and " +
                "cpw.paymentType.code = 'monthly_payment' and cpw.paymentDate >= ?3 and cpw.paymentDate <= ?4 ")
    ContractPaymentConfirmationToDo findByCptAndSourceAndPaymentTypeAndDate(
            ContractPaymentTerm cpt, ContractPaymentConfirmationToDo.Source source, Date s, Date e);

    @Query("select todo from ContractPaymentConfirmationToDo todo " +
            "where todo.contractPaymentTerm = ?1 and todo.source in ?2 and todo.disabled = 0 and " +
                "todo.creditCardOffer = true and todo.showOnERP = 0 " +
            "order by todo.creationDate desc")
    List<ContractPaymentConfirmationToDo> findAllConfirmationToDoByContractPaymentTermAndSource(
            ContractPaymentTerm cpt, List<ContractPaymentConfirmationToDo.Source> sourceList);

    @Query("select todo from ContractPaymentConfirmationToDo todo " +
            "where todo.contractPaymentTerm = ?1 and todo.creditCardOffer = true and todo.disabled = false and " +
            "todo.creditCardOffer = true and todo.showOnERP = false " +
            "order by todo.creationDate desc")
    List<ContractPaymentConfirmationToDo> findAllConfirmationToDoByContractPaymentTermAndCreditCardOffer(ContractPaymentTerm cpt);

    @Query("select c from ContractPaymentConfirmationToDo c " +
            "where c.transferReference in ?1 and c.payingOnline = true and " +
                "c.paymentMethod = 'Card' and c.confirmed = false and c.showOnERP = true and c.source = 'CLIENT_REFUND'")
    List<ContractPaymentConfirmationToDo> findByOnlineCardPaymentAndTransferReference(List<String> codes);

    @Query("select new map(" +
            "todo.id as todoId, p.id as paymentId, " +
                "p.typeOfPayment.name as paymentType, " +
                "p.amountOfPayment as paymentAmount) " +
            "from ContractPaymentWrapper cpw " +
            "join cpw.contractPaymentConfirmationToDo todo " +
            "inner join Payment p on p.id = " +
                "case " +
                    "when cpw.generatedPaymentId is null and cpw.replacedFuturePaymentId is not null " +
                        "then cpw.replacedFuturePaymentId " +
                    "when cpw.generatedPaymentId is null and cpw.replacedBouncedPaymentId is not null and " +
                            "exists (select 1 from Payment p1 where p1.replacementFor.id = cpw.replacedBouncedPaymentId) " +
                        "then (select max(id) from Payment p1 " +
                            "where p1.replacementFor.id = cpw.replacedBouncedPaymentId and p1.methodOfPayment = 'CARD') " +
                    "when cpw.generatedPaymentId is null and cpw.replacedBouncedPaymentId is not null " +
                        "then cpw.replacedBouncedPaymentId " +
                    "else cpw.generatedPaymentId " +
                "end " +
            "where todo.id in ?1")
    List<Map> findOnlineCardStatementGridInfoByTodoIds(List<Long> ids);

    @Query("select new map(" +
                "todo.id as todoId, todo.creationDate as paymentDate, todo.contractPaymentTerm.contract.client.name as clientName, " +
                "todo.transferReference as transferReference, sum(cpw.amount) as amount) " +
            "from ContractPaymentWrapper cpw " +
            "join cpw.contractPaymentConfirmationToDo todo " +
            "where todo.paymentMethod = 'Card' and todo.confirmed = false and todo.showOnERP = true and todo.source = 'CLIENT_REFUND' " +
            "group by todo.id " +
            "having sum(cpw.amount) = ?1")
    Page<Map> getTodosForMatchOnlineStatementForRefundPayment(Double d, Pageable pageable);

    @Query("select distinct todo from ContractPaymentWrapper cpw " +
            "join cpw.contractPaymentConfirmationToDo todo " +
            "join todo.contractPaymentTerm.contract c " +
            "where todo.id > ?1 and todo.disabled = false and " +
            "todo.paymentMethod = 'CARD' and todo.payingOnline = true and todo.source in ('ERP', 'PAYMENT_REMINDER') and " +
            "not exists (select 1 from Payment p " +
            "where p.contract = c and p.typeOfPayment = cpw.paymentType and " +
                "p.dateOfPayment = cpw.paymentDate and " +
                "p.status = 'PRE_PDP' and p.methodOfPayment = 'CARD')")
    Page<ContractPaymentConfirmationToDo> findOnlineCardTodosAndPaymentStatusChanged(Long lastId, Pageable pageable);

    @Query("select todo " +
            "from ContractPaymentWrapper cpw " +
            "join cpw.contractPaymentConfirmationToDo todo " +
            "where cpw.replacedBouncedPaymentId = ?1 and todo.source = 'BOUNCED_PAYMENT_FLOW' and todo.disabled = false")
    ContractPaymentConfirmationToDo findToDoForBouncedPaymentFlow(Long paymentID);

    @Query("select todo " +
            "from ContractPaymentWrapper cpw " +
            "join cpw.contractPaymentConfirmationToDo todo " +
            "where cpw.replacedFuturePaymentId = ?1 and todo.source = 'FAQ' and todo.disabled = false")
    ContractPaymentConfirmationToDo findToDoForFAQ(Long paymentID);

    @Query("select distinct todo from ContractPaymentWrapper cpw " +
            "join cpw.contractPaymentConfirmationToDo todo " +
            "join todo.contractPaymentTerm.contract c " +
            "where todo.id > ?1 and todo.disabled = false and todo.source <> 'CLIENT_REFUND' and " +
                "todo.paymentMethod = 'CARD' and todo.payingOnline = true and " +
                "exists (select 1 from Payment p  " +
                    "where p.contract = c and p.typeOfPayment = cpw.paymentType and "  +
                        "p.dateOfPayment = cpw.paymentDate and p.status = 'RECEIVED')")
    Page<ContractPaymentConfirmationToDo> findOnlineCardTodosAndPaymentStatusChangedToReceived(Long lastId, Pageable pageable);


    @Query("select count(todo.id) > 0 " +
            "from ContractPaymentWrapper cpw " +
            "join cpw.contractPaymentConfirmationToDo todo " +
            "join todo.contractPaymentTerm.contract c " +
            "where c.id = ?1 and cpw.paymentType.id = ?2 and cpw.paymentDate = ?3 and " +
                "todo.disabled = false and todo.showOnERP = false and todo.paymentMethod = 'CARD' and todo.payingOnline = true")
    boolean existsOnlineCardTodosByPayment(Long contractId, Long paymentId, Date d);

    @Query("select distinct todo " +
            "from ContractPaymentWrapper cpw " +
            "join cpw.contractPaymentConfirmationToDo todo " +
            "join todo.contractPaymentTerm.contract c " +
            "where c.id = ?1 and cpw.paymentType.code = ?2 and cpw.paymentDate = ?3 and " +
                "todo.disabled = false and todo.showOnERP = false and todo.paymentMethod = 'CARD' and " +
                "todo.payingOnline = true and todo.source = ?4")
    List<ContractPaymentConfirmationToDo> findOnlineCardTodosBySource(Long contractId, String paymentType, Date d, Source source);

    @Query("select distinct todo " +
            "from ContractPaymentWrapper cpw " +
            "join cpw.contractPaymentConfirmationToDo todo " +
            "join todo.contractPaymentTerm.contract c " +
            "where c.id = ?1 and cpw.paymentType.id = ?2 and cpw.paymentDate = ?3 and " +
                "todo.disabled = false and todo.showOnERP = false and todo.paymentMethod = 'CARD' and todo.payingOnline = true")
    List<ContractPaymentConfirmationToDo> findOnlineCardTodosByPayment(Long contractId, Long paymentId, Date d);

    @Query("select count(todo.id) > 0 " +
            "from ContractPaymentWrapper cpw " +
            "join cpw.contractPaymentConfirmationToDo todo " +
            "where cpw.replacedBouncedPaymentId = ?1 and todo.showOnERP = true and todo.creationDate > ?2")
    boolean existsByReplacedBouncedPaymentIdAndShowOnErpTrueAndCreationDate(Long paymentID, Date d);

    @Query("select todo " +
            "from ContractPaymentWrapper cpw " +
            "join cpw.contractPaymentConfirmationToDo todo " +
            "where (cpw.generatedPaymentId = ?1 or cpw.replacedBouncedPaymentId = ?1) and todo.disabled = false and " +
            "todo.source in ('ERP', 'PAYMENT_REMINDER') and " +
            "todo.paymentMethod = 'CARD' and todo.payingOnline = true ")
    List<ContractPaymentConfirmationToDo> getTodosRelatedToPaymentDeleted(Long paymentId);

    ContractPaymentConfirmationToDo findFirstByRelatedEntityIdAndRelatedEntityTypeAndDisabledFalseAndShowOnERPFalse(Long relatedEntityId, String relatedEntityType);

    @Query("select new Map(f as flow, todo as t) " +
            "from ContractPaymentWrapper cpw " +
            "join cpw.contractPaymentConfirmationToDo todo " +
            "inner join FlowProcessorEntity f on f.contractPaymentConfirmationToDo = todo " +
            "where cpw.generatedPaymentId = ?1 and (todo.disabled = false or (f.stopped = false and f.completed = false)) and " +
            "f.flowEventConfig.name = 'CLIENTS_PAYING_VIA_Credit_Card' and f.currentSubEvent.name in ?2 ")
    List<Map<String, Object>> findFlowsRelatedToRecurringPaymentDeleted(
            Long paymentId, List<FlowSubEventConfig.FlowSubEventName> recurringFailureFlows);

    @Query("select new Map(f.currentSubEvent.name as subEventName, todo.id as toDoId) " +
            "from ContractPaymentWrapper cpw " +
            "inner join cpw.contractPaymentConfirmationToDo todo " +
            "inner join FlowProcessorEntity f on f.contractPaymentConfirmationToDo = todo " +
            "where cpw.generatedPaymentId = ?1 and " +
                "f.flowEventConfig.name = 'CLIENTS_PAYING_VIA_Credit_Card' and f.currentSubEvent.name in ?2 ")
    List<Map<String, Object>> findFlowsRelatedToRecurringPayment(
            Long paymentId, List<FlowSubEventConfig.FlowSubEventName> recurringFailureFlows);

    @Query("select distinct todo from ContractPaymentConfirmationToDo todo " +
            "inner join FlowProcessorEntity f on f.contractPaymentConfirmationToDo = todo " +
            "where todo.id > ?1 and todo.disabled = false and todo.showOnERP = false and (f.stopped = true or f.completed = true) and " +
            "not exists(select 1 from FlowProcessorEntity f1 " +
                    "where f.id <> f1.id and f1.contractPaymentConfirmationToDo.id = todo.id and " +
                        "f1.stopped = false and f1.completed = false)")
    Page<ContractPaymentConfirmationToDo> findToDoWithFinishedFlows(Long lastId, Pageable pageable);

    @Query("select distinct todo from ContractPaymentWrapper cpw " +
            "join cpw.contractPaymentConfirmationToDo todo " +
            "where ((cpw.generatedPaymentId is null and cpw.replacedFuturePaymentId = ?1) or " +
                    "(cpw.generatedPaymentId is null and cpw.replacedBouncedPaymentId is not null and " +
                        "exists (select 1 from Payment p where p.replacementFor.id = cpw.replacedBouncedPaymentId and p.id = ?1)) or " +
                    "(cpw.generatedPaymentId = ?1)) and " +
                "todo.payingOnline = true and todo.paymentMethod = 'Card' and todo.showOnERP = true and todo.source <> 'CLIENT_REFUND'")
    List<ContractPaymentConfirmationToDo> findTodosByPayment(Long paymentId);

    @Query("select count(cpw.id) > 0 " +
            "from ContractPaymentWrapper cpw " +
            "where cpw.generatedPaymentId = ?1 and cpw.contractPaymentConfirmationToDo.ignoreSendNotification = true")
    boolean existsByGeneratedPaymentIdAndIgnoreSendNotificationTrue(Long paymentId);

    @Query("select distinct new map(p.id as paymentId, todo as todo) " +
            "from Payment p " +
            "inner join ContractPaymentWrapper cpw on " +
                "(cpw.generatedPaymentId = p.id or " +
                    "(cpw.generatedPaymentId is null and cpw.replacedFuturePaymentId = p.id) or " +
                    "(cpw.generatedPaymentId is null and cpw.replacedBouncedPaymentId is not null and " +
                        "p.replacementFor.id = cpw.replacedBouncedPaymentId)) " +
            "inner join cpw.contractPaymentConfirmationToDo todo " +
            "where p.id in (?1) and p.methodOfPayment = 'CARD' and p.status not in ('RECEIVED', 'DELETED') and " +
                "p.replaced = false and todo.paymentMethod = 'CARD' and todo.payingOnline = true and " +
                "todo.showOnERP = false and todo.disabled = false and todo.source <> 'CLIENT_REFUND' " +
            "group by p " +
            "order by max(todo.creationDate) desc")
    List<Map<String, Object>> findConfirmationToDoViaPayment(long[] paymentIds);

    @Query("select distinct t from Payment p " +
            "inner join p.contract c " +
            "inner join ContractPaymentWrapper cpw on " +
                "(cpw.generatedPaymentId = p.id or cpw.replacedFuturePaymentId = p.id or " +
                "(cpw.replacedBouncedPaymentId is not null and p.id = cpw.replacedBouncedPaymentId)) " +
            "inner join cpw.contractPaymentConfirmationToDo t " +
            "where p.id = ?1 and p.status <> 'DELETED' and p.recurring = false and t.paymentMethod = 'CARD' and t.payingOnline = true and " +
                "t.showOnERP = false and t.disabled = false and t.source <> 'CLIENT_REFUND' and " +
                "(t.reactivationPayment = true or c.dateOfTermination is null or c.dateOfTermination <= t.creationDate)" +
            "group by p " +
            "order by t.creationDate desc")
    List<ContractPaymentConfirmationToDo> findToDosByPaymentsCheckedByContract(Long paymentId);

    @Query("select distinct t from ContractPaymentWrapper cpw " +
            "inner join cpw.contractPaymentConfirmationToDo t " +
            "inner join t.contractPaymentTerm.contract c " +
            "inner join FlowProcessorEntity f ON f.contractPaymentConfirmationToDo = t " +
            "where c = ?1 AND cpw.paymentDate = ?2 AND cpw.amount = ?3 and cpw.paymentType.code = ?4 and " +
                "t.paymentMethod = 'CARD' and t.payingOnline = true and f.stopped = false and f.completed = false and " +
                "t.showOnERP = false and t.disabled = false and t.source <> 'CLIENT_REFUND' and " +
                "(t.reactivationPayment = true or c.dateOfTermination is null or c.dateOfTermination <= t.creationDate) " +
            "order by t.creationDate desc")
    List<ContractPaymentConfirmationToDo> findOnlineMonthlyCardPaymentTodo(
            Contract contract, Date date, Double amount, String type);

    @Query("select distinct new map(p.id as paymentId, t.source as todoSource, " +
                "(case when t.showOnERP = true then 0 else 1 end) as priority) " +
            "from Payment p " +
            "inner join p.contract c " +
            "inner join ContractPaymentWrapper cpw on " +
                "(cpw.generatedPaymentId = p.id or cpw.replacedFuturePaymentId = p.id or " +
                "(cpw.replacedBouncedPaymentId is not null and p.id = cpw.replacedBouncedPaymentId)) " +
            "inner join cpw.contractPaymentConfirmationToDo t " +
            "where p.id = ?1 and p.status <> 'DELETED' and t.paymentMethod = 'CARD' and " +
                "t.payingOnline = true and t.source <> 'CLIENT_REFUND' and t.disabled = false " +
            "group by p " +
            "order by priority asc, t.creationDate desc")
    List<Map<String, Object>> getSourceFromToDoByPaymentId(Long paymentId);

    @Query("select distinct todo from ContractPaymentConfirmationToDo todo " +
            "where todo.id not in :idsToExclude and todo.contractPaymentTerm.contract.id in :contractsIds and " +
            "   ((:authorizationCode is not null and (todo.authorizationCode = :authorizationCode or " +
            "                                           todo.cleanAuthorizationCode = :cleanAuthorizationCode) and " +
            "       exists (select 1 from EPaymentTransaction e " +
            "                   where (e.authorizationCode = :authorizationCode or " +
            "                           e.authorizationCode = :cleanAuthorizationCode) and " +
            "                         e.provider = 'PAYTABS')) or " +
            "   (:transferReference is not null and todo.transferReference = :transferReference and " +
            "       exists (select 1 from EPaymentTransaction e " +
            "                   where e.transReference = :transferReference and e.provider = 'CHECKOUT')))")
    List<ContractPaymentConfirmationToDo> getByContractAndAuthorizationCode(
            @Param("idsToExclude") List<Long> idsToExclude,
            @Param("contractsIds") List<Long> contractsIds,
            @Param("authorizationCode") String authorizationCode,
            @Param("cleanAuthorizationCode") String cleanAuthorizationCode,
            @Param("transferReference") String transferReference);

    @Query("select distinct todo from ContractPaymentWrapper cpw " +
            "join cpw.contractPaymentConfirmationToDo todo " +
            "where todo.disabled = false and todo.showOnERP = false and " +
                "cpw.generatedPaymentId = ?1 and todo.source = ?2 " +
            "order by todo.creationDate desc")
    List<ContractPaymentConfirmationToDo> findToDoForRecurringCaptureByPaymentId(Long paymentID, Source source);

    @Query("select todo from ContractPaymentConfirmationToDo todo " +
            "where todo.contractPaymentTerm = ?1 and todo.disabled = false and todo.showOnERP = false and " +
                "todo.source in ?2 and todo.purpose in ('CC_APP_ADD_NEW_CARD', 'CC_APP_RECURRING_CLIENT_INITIATED')")
    List<ContractPaymentConfirmationToDo> findToDoWaitingRecurringToken(ContractPaymentTerm cpt, List<Source> sources);

    @Query("select count(cpw.id) > 0 from ContractPaymentWrapper cpw " +
            "join cpw.contractPaymentConfirmationToDo todo " +
            "inner join FlowProcessorEntity f on f.contractPaymentConfirmationToDo = todo " +
            "where todo.contractPaymentTerm.contract = ?1 and cpw.paymentType.code = 'monthly_payment' and " +
                "cpw.paymentDate >= ?2 and cpw.paymentDate < ?3 and todo.disabled = false and todo.showOnERP = false and " +
                "f.currentSubEvent.name in ('INITIAL_FLOW_FOR_DDB', 'INITIAL_FLOW_FOR_DDA') and " +
                "f.stopped = false and f.completed = false")
    boolean existsPaymentInInitialFlow(Contract c, Date startOfDate, Date endOfDate);

    @Query("select distinct new map(todo.id, c.id as cId, c.client.name as clientName, count(todo.id) as numbersTodo) " +
            "from ContractPaymentWrapper w " +
            "join w.contractPaymentConfirmationToDo todo " +
            "join todo.contractPaymentTerm.contract c " +
            "where todo.source = 'SWITCH_NATIONALITY' and todo.creationDate >= ?1 and todo.showOnERP = true and " +
            "(w.paymentType.code = 'upgrading_nationality' or w.includeUpgradingFee = true)" +
            "group by c.id " +
            "having count(todo.id) > 0")
    List<Map<String, Object>> findContractsSwitchingNationalityLastDay(Date date);

    @Query("select todo " +
            "from ContractPaymentWrapper cpw " +
            "join cpw.contractPaymentConfirmationToDo todo " +
            "where todo.contractPaymentTerm.contract = ?1 and cpw.paymentType.code = 'insurance' and " +
                "todo.disabled = false and todo.showOnERP = false and todo.paymentMethod = 'CARD'")
    List<ContractPaymentConfirmationToDo> getTodosWithInsuranceWrapper(Contract c);

    @Query("select todo " +
            "from ContractPaymentWrapper cpw " +
            "join cpw.contractPaymentConfirmationToDo todo " +
            "where todo.contractPaymentTerm.contract = ?1 and todo.payingOnline = true and " +
                "cpw.includeWorkerSalary = true and todo.showOnERP = false and todo.disabled = false and cpw.paymentDate >= ?2")
    List<ContractPaymentConfirmationToDo> findByContractAndDisabledAndWorkerSalaryAndSourceAndDateOfPayment(
            Contract contract, Date nextMonth);

    int countByContractPaymentTerm_ContractAndSourceAndShowOnERPTrue(Contract contract, Source source);

    @Query("select count(todo.id) > 0 " +
            "from ContractPaymentConfirmationToDo todo " +
            "join todo.contractPaymentTerm.contract c " +
            "where c.id = ?1 and todo.disabled = false and todo.showOnERP = false and " +
                "todo.paymentMethod = 'CARD' and todo.payingOnline = true and todo.source in ('ERP', 'PAYMENT_REMINDER') and " +
                "exists (select 1 from FlowProcessorEntity f " +
                    "where f.contractPaymentConfirmationToDo = todo and f.stopped = true and f.completed = false and " +
                          "(f.causedTermination = true or f.stoppedDueContractTerminated = true))")
    boolean existsValidOnlineReminder(Long contractId);

    @Query("select new Map(todo as todo, f as flow) " +
            "from FlowProcessorEntity f " +
            "join f.contractPaymentConfirmationToDo todo " +
            "join todo.contractPaymentTerm.contract c " +
            "where c.id = :contractId and (:todoId is null or todo.id <> :todoId) and " +
                "todo.disabled = false and todo.showOnERP = false and todo.paymentMethod = 'CARD' and " +
                "todo.payingOnline = true and todo.source in ('ERP', 'PAYMENT_REMINDER') and " +
                "f.stopped = true and f.completed = false and (f.causedTermination = true or f.stoppedDueContractTerminated = true)")
    List<Map<String, Object>> findValidOnlineReminder(@Param("contractId") Long contractId, @Param("todoId") Long todoId);

    @Query("select count(todo.id) > 0 from ContractPaymentWrapper cpw " +
            "join cpw.contractPaymentConfirmationToDo todo " +
            "where ((cpw.generatedPaymentId is null and cpw.replacedFuturePaymentId = ?1) or " +
                "(cpw.generatedPaymentId is null and cpw.replacedBouncedPaymentId is not null and " +
                    "exists (select 1 from Payment p where p.replacementFor.id = cpw.replacedBouncedPaymentId and p.id = ?1)) or " +
                "(cpw.generatedPaymentId = ?1)) and " +
            "todo.payingOnline = true and todo.paymentMethod = 'Card' and todo.creditCardOffer = true and todo.showOnERP = true")
    boolean isPaymentRelatedToCreditCardOffer(Long paymentId);

    @Query("select count(w.id) > 0 from ContractPaymentWrapper w " +
            "join w.contractPaymentConfirmationToDo todo " +
            "inner join FlowProcessorEntity f on f.contractPaymentConfirmationToDo = todo " +
            "where todo.contractPaymentTerm.contract = ?1 and todo.disabled = false and " +
                "todo.showOnERP = false and todo.confirmed = false and " +
                "f.stopped = false and f.completed = false and " +
                "w.paymentType.code = ?2 and w.paymentDate between ?3 and ?4")
    boolean existRunningFlowHandledPayment(
            Contract c, String paymentTypeCode, Date startDate, Date endDate);
}