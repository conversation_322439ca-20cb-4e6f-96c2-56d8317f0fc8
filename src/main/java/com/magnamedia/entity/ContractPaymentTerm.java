package com.magnamedia.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.entity.WordTemplate;
import com.magnamedia.core.helper.HistorySelectQuery;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.serialize.IdLabelCodeSerializer;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.entity.serializer.HousemaidSerilizer;
import com.magnamedia.module.type.ContractPaymentTermReason;
import com.magnamedia.service.CalculateDiscountsWithVatService;
import com.magnamedia.service.ContractPaymentTermDetailsService;
import com.magnamedia.service.SwitchingNationalityService;
import org.apache.commons.lang3.BooleanUtils;
import org.hibernate.envers.NotAudited;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;

import javax.persistence.*;
import javax.validation.constraints.Min;
import java.util.*;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 * <AUTHOR> kanaan <<EMAIL>> Created on Dec 11, 2018 Jirra
 *         ACC-329
 * 
 */
@Entity
public class ContractPaymentTerm extends AbstractPaymentTerm {
    private static final java.util.logging.Logger logger = Logger.getLogger(ContractPaymentTerm.class.getName());

    @ManyToOne(optional = false, fetch = FetchType.LAZY)
    @JsonSerialize(using = HousemaidSerilizer.class)
    private Housemaid housemaid;

    @NotAudited
    @ManyToOne
    // @JsonSerialize(using = IdLabelSerializer.class)
    private Contract contract;

    //Jirra ACC-2161
    @ManyToOne
    @JsonSerialize(using = IdLabelSerializer.class)
    private Replacement replacement;

    @ManyToOne(optional = false, fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private PaymentTermConfig paymentTermConfig;

    @Column(nullable = false)
    @Min(0)
    private double firstMonthPayment;

    @Column(nullable = false)
    private boolean isProRated = false;

    //Jirra ACC-1435
    @Column(nullable = false)
    private boolean ddMsgsDisabled = false;

    @Column
    private String eid;

    @Column
    private String ibanNumber;

    @Column
    private String bankName;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelCodeSerializer.class)
    private PicklistItem bank;

    @Column
    private String accountName;

    @Column(nullable = false)
    private boolean isActive = true;

    @Column
    private Integer proRatedDays;

    @Column(nullable = false)
    private boolean isEOM = false;

    @Column
    private Double additionalDiscount;

    //Jirra SAL-2750
    @Column
    private Integer additionalDiscountMonths;

    @Column
    private Double creditNote;

    @Column
    private Integer creditNoteMonths;

    @Column
    @Lob
    private String additionalDiscountNotes;

    //Jirra ACC-1435
    @Column(nullable = false)
    private boolean isFixedByVAT = false;

    @Column
    @Enumerated(EnumType.STRING)
    private ContractPaymentTermReason reason;

    //Jirra ACC-1092
    @Column(nullable = false)
    private Boolean spouseWillSignDD = false;

    //Jirra SAL-2034
    @Column(columnDefinition = "boolean default false")
    private Boolean receiptSent = false;

    @Column(columnDefinition = "boolean default false")
    private Boolean isEidRejected = false;

    @Column(columnDefinition = "boolean default false")
    private Boolean isIBANRejected = false;

    @Column(columnDefinition = "boolean default false")
    private Boolean isAccountHolderRejected = false;

    //Jirra ACC-2357
    @JsonSerialize(using = IdLabelCodeSerializer.class)
    @ManyToOne(fetch = FetchType.LAZY)
    private PicklistItem eidRejectionReason;

    @JsonSerialize(using = IdLabelCodeSerializer.class)
    @ManyToOne(fetch = FetchType.LAZY)
    private PicklistItem ibanRejectionReason;

    @JsonSerialize(using = IdLabelCodeSerializer.class)
    @ManyToOne(fetch = FetchType.LAZY)
    private PicklistItem accountNameRejectionReason;

    @Column(columnDefinition = "boolean default false")
    private Boolean isAfterSignsMsgSent = false;

    @Column(columnDefinition = "boolean default false")
    private Boolean switchingToPhilipino = false;

    //Jirra ACC-1778
    @Column(columnDefinition = "boolean default false")
    private Boolean bankInfoTextBased = false;

    //Jirra ACC-2822
    @Column(columnDefinition = "boolean default false")
    private Boolean switchedOrReplacedNationality = false;

    //Jirra ACC-2822
    @Column
    private Date switchOrReplaceNationalityDate;

    //Jirra ACC-2921
    @Column(columnDefinition = "boolean default false")
    private Boolean switchedBankAccount = false;

    //Jira SAL-2922
    @Column
    private Double visaFees;

    @OneToMany(cascade = CascadeType.ALL, mappedBy = "contractPaymentTerm", fetch = FetchType.LAZY)
    private List<ContractPaymentType> contractPaymentTypes;
    
    /*ACC-3597**/
    @Column
    private Date amendedDate; // contains the date of OEC amending

    @Column(columnDefinition = "boolean default false")
    private Boolean overrideSwitchNationalityValidation = false;

    @Column
    private Long ddcId;

    @Transient // ACC-6633
    private boolean forceGenerateDds = false;

    @Transient // ACC-7151
    private List<ContractPaymentTermDetails> contractPaymentTermDetails = null;

    @Column
    private String sourceId;

    @Column
    @Lob
    private String sourceInfo;

    @Column
    private Double sourceAmount;

    public Housemaid getHousemaid() {
        return housemaid;
    }

    public void setHousemaid(Housemaid housemaid) {
        this.housemaid = housemaid;
    }

    public Contract getContract() {
        return contract;
    }

    public void setContract(Contract contract) {
        this.contract = contract;
    }

    public Replacement getReplacement() {
        return replacement;
    }

    public void setReplacement(Replacement replacement) {
        this.replacement = replacement;
    }

    public PaymentTermConfig getPaymentTermConfig() {
        return paymentTermConfig;
    }

    public void setPaymentTermConfig(PaymentTermConfig paymentTermConfig) {
        this.paymentTermConfig = paymentTermConfig;
    }

    public void setPaymentTermConfigWithValues(PaymentTermConfig paymentTermConfig) {
        this.paymentTermConfig = paymentTermConfig;
        if (paymentTermConfig == null) return;

        this.setNationality(paymentTermConfig.getNationality());
        this.setContractProspectType(paymentTermConfig.getContractProspectType());
        this.setType(paymentTermConfig.getType());
        this.setPeriodicalAdditionalDiscount(paymentTermConfig.getPeriodicalAdditionalDiscount());
        this.setWeeklyAmount(paymentTermConfig.getWeeklyAmount());
        this.setDailyRateAmount(paymentTermConfig.getDailyRateAmount());
        this.setCptFamily(paymentTermConfig.getCptFamily());

        if (paymentTermConfig.getPaymentTypeConfigs() != null) {
            List<ContractPaymentType> types = new ArrayList();
            for (PaymentTypeConfig paymentTypeConfig : paymentTermConfig.getPaymentTypeConfigs()) {
                types.add(new ContractPaymentType(paymentTypeConfig));
            }

            this.setContractPaymentTypes(types);
        }

    }

    public double getFirstMonthPayment() {
        return firstMonthPayment;
    }

    public void setFirstMonthPayment(double firstMonthPayment) {
        this.firstMonthPayment = firstMonthPayment;
    }

    public boolean isIsProRated() {
        return isProRated;
    }

    public void setIsProRated(boolean isProRated) {
        this.isProRated = isProRated;
    }

    public String getEid() {
        return eid;
    }

    public void setEid(String eid) {
        if (getEid() == null && eid != null && getIsEidRejected()) { // acc-1542
            setIsEidRejected(false);
            setEidRejectionReason(null);
        }
        
        this.eid = eid;
    }

    public String getIbanNumber() {
        return ibanNumber;
    }

    public void setIbanNumber(String ibanNumber) {
        if (getIbanNumber() == null && ibanNumber != null && getIsIBANRejected()) { // acc-1542
            setIsIBANRejected(false);
            setIbanRejectionReason(null);
        }
        
        this.ibanNumber = ibanNumber;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public PicklistItem getBank() {
        return bank;
    }

    public void setBank(PicklistItem bank) {
        this.bank = bank;
    }

    public String getAccountName() {
        return accountName;
    }

    public void setAccountName(String accountName) {
        if (getAccountName() == null && accountName != null && getIsAccountHolderRejected()) { // acc-1542
            setIsAccountHolderRejected(false);
            setAccountNameRejectionReason(null);
        }
        
        this.accountName = accountName;
    }

    public boolean isIsActive() {
        return isActive;
    }

    public void setIsActive(boolean isActive) {
        this.isActive = isActive;
    }

    public boolean isIsEOM() {
        return isEOM;
    }

    public void setIsEOM(boolean isEOM) {
        this.isEOM = isEOM;
    }

    public Double getAdditionalDiscount() {
        return additionalDiscount;
    }

    public void setAdditionalDiscount(Double additionalDiscount) {
        this.additionalDiscount = additionalDiscount;
    }

    public Integer getAdditionalDiscountMonths() {
        return additionalDiscountMonths;
    }

    public void setAdditionalDiscountMonths(Integer additionalDiscountMonths) {
        this.additionalDiscountMonths = additionalDiscountMonths;
    }

    public Integer getAdditionalDiscountMonthsCount(String paymentTypeCode) {
        if (!isAffectedByAdditionalDiscount(paymentTypeCode)) return 0; // ACC-3272
        if (!BooleanUtils.toBoolean(this.getPeriodicalAdditionalDiscount())) return 1; // ACC-3272
        if (additionalDiscountMonths != null) return additionalDiscountMonths;
        if (this.getDiscount() != 0) return this.getDiscountEffectiveAfter();

        return contract.getPaymentsDuration();
    }

    public String getAdditionalDiscountNotes() {
        return additionalDiscountNotes;
    }

    public void setAdditionalDiscountNotes(String additionalDiscountNotes) {
        this.additionalDiscountNotes = additionalDiscountNotes;
    }

    public Integer getProRatedDays() {
        return proRatedDays == null ? 0 : proRatedDays;
    }

    public void setProRatedDays(Integer proRatedDays) {
        this.proRatedDays = proRatedDays;
    }

    //Jirra ACC-1092
    public Boolean getSpouseWillSignDD() {
        return spouseWillSignDD != null && spouseWillSignDD;
    }

    public void setSpouseWillSignDD(Boolean spouseWillSignDD) {
        this.spouseWillSignDD = spouseWillSignDD;
    }

    public boolean isProRated() {
        return isProRated;
    }

    public void setProRated(boolean proRated) {
        isProRated = proRated;
    }

    public boolean isDdMsgsDisabled() {
        return ddMsgsDisabled;
    }

    public void setDdMsgsDisabled(boolean ddMsgsDisabled) {
        this.ddMsgsDisabled = ddMsgsDisabled;
    }

    public boolean isActive() {
        return isActive;
    }

    public void setActive(boolean active) {
        isActive = active;
    }


    public boolean isFixedByVAT() {
        return isFixedByVAT;
    }

    public boolean isIsFixedByVAT() {
        return isFixedByVAT;
    }

    public void setFixedByVAT(boolean fixedByVAT) {
        isFixedByVAT = fixedByVAT;
    }

    public ContractPaymentTermReason getReason() {
        return reason;
    }

    public void setReason(ContractPaymentTermReason reason) {
        this.reason = reason;
    }

    public Boolean getReceiptSent() {
        return receiptSent;
    }

    public void setReceiptSent(Boolean receiptSent) {
        this.receiptSent = receiptSent;
    }

    public Boolean getIsEidRejected() {
        return isEidRejected;
    }

    public void setIsEidRejected(Boolean isEidRejected) {
        this.isEidRejected = isEidRejected;
    }

    public Boolean getIsIBANRejected() {
        return isIBANRejected;
    }

    public void setIsIBANRejected(Boolean isIBANRejected) {
        this.isIBANRejected = isIBANRejected;
    }

    public Boolean getIsAccountHolderRejected() {
        return isAccountHolderRejected;
    }

    public void setIsAccountHolderRejected(Boolean isAccountHolderRejected) {
        this.isAccountHolderRejected = isAccountHolderRejected;
    }

    public PicklistItem getEidRejectionReason() {
        return eidRejectionReason;
    }

    public void setEidRejectionReason(PicklistItem eidRejectionReason) {
        this.eidRejectionReason = eidRejectionReason;
    }

    public PicklistItem getIbanRejectionReason() {
        return ibanRejectionReason;
    }

    public void setIbanRejectionReason(PicklistItem ibanRejectionReason) {
        this.ibanRejectionReason = ibanRejectionReason;
    }

    public PicklistItem getAccountNameRejectionReason() {
        return accountNameRejectionReason;
    }

    public void setAccountNameRejectionReason(PicklistItem accountNameRejectionReason) {
        this.accountNameRejectionReason = accountNameRejectionReason;
    }

    public Boolean getIsAfterSignsMsgSent() {
        return isAfterSignsMsgSent;
    }

    public void setIsAfterSignsMsgSent(Boolean isAfterSignsMsgSent) {
        this.isAfterSignsMsgSent = isAfterSignsMsgSent;
    }

    public Boolean getSwitchingToPhilipino() {
        return switchingToPhilipino != null && switchingToPhilipino;
    }

    public void setSwitchingToPhilipino(Boolean switchingToPhilipino) {
        this.switchingToPhilipino = switchingToPhilipino;
    }

    public Boolean getBankInfoTextBased() {
        return bankInfoTextBased;
    }

    public void setBankInfoTextBased(Boolean bankInfoTextBased) {
        this.bankInfoTextBased = bankInfoTextBased;
    }

    public Boolean getSwitchedOrReplacedNationality() {
        return switchedOrReplacedNationality;
    }

    public void setSwitchedOrReplacedNationality(Boolean switchedOrReplacedNationality) {
        this.switchedOrReplacedNationality = switchedOrReplacedNationality;
    }

    public Date getSwitchOrReplaceNationalityDate() {
        return switchOrReplaceNationalityDate;
    }

    public void setSwitchOrReplaceNationalityDate(Date switchOrReplaceNationalityDate) {
        this.switchOrReplaceNationalityDate = switchOrReplaceNationalityDate;
    }

    public Boolean getSwitchedBankAccount() {
        return switchedBankAccount != null && switchedBankAccount;
    }

    public void setSwitchedBankAccount(Boolean switchedBankAccount) {
        this.switchedBankAccount = switchedBankAccount;
    }

    public Double getVisaFees() {
        return visaFees;
    }

    public void setVisaFees(Double visaFees) {
        this.visaFees = visaFees;
    }

    @JsonIgnore
    public DirectDebit getLastConfirmedDD() {
        SelectQuery<DirectDebit> query = new SelectQuery(DirectDebit.class);
        query.filterBy("contractPaymentTerm", "=", this);

        List<DirectDebit> directDebits = query.execute();

        if (directDebits == null || directDebits.isEmpty()) return null;

        List<Long> ddIDS = directDebits.stream().map(directDebit -> directDebit.getId())
                .collect(Collectors.toList());

        HistorySelectQuery<DirectDebit> historySelectQuery = new HistorySelectQuery(DirectDebit.class);
        historySelectQuery.filterBy("id", "in", ddIDS);
        historySelectQuery.filterBy("confirmedBankInfo", "=", Boolean.TRUE);

        historySelectQuery.filterByChanged("confirmedBankInfo");

        historySelectQuery.sortBy("lastModificationDate", false, true);
        historySelectQuery.setLimit(1);

        List<DirectDebit> oldDDs = historySelectQuery.execute();

        if (oldDDs == null || oldDDs.isEmpty()) return null;

        return oldDDs.get(0);
    }

    @JsonIgnore
    public Map<String, Map> getPricesMap() {
        SwitchingNationalityService switchingNationalityService = Setup.getApplicationContext().getBean(SwitchingNationalityService.class);
        Map<String, Map> prices = new LinkedHashMap();

        DateTime pricingStartDate = new DateTime().plusMonths(1).dayOfMonth().withMinimumValue().withTimeAtStartOfDay();
        DateTime pricingDate = pricingStartDate;

        DateTime discountedPeriodStartDate = new DateTime(Setup.getApplicationContext()
                .getBean(CalculateDiscountsWithVatService.class)
                .getDiscountStartDateInMillis(this.getContract().getStartOfContract(), true, this));

        if (pricingDate.isBefore(discountedPeriodStartDate)) {
            Double premiumPeriodPrice = getMonthlyPayment();

            Map premiumPeriodMap = new HashMap();
            premiumPeriodMap.put("startDate", pricingDate.toDate());
            premiumPeriodMap.put("expiryDate", discountedPeriodStartDate.minusMonths(1).dayOfMonth().withMaximumValue().toDate());
            premiumPeriodMap.put("price", premiumPeriodPrice);

            prices.put("premiumPeriod", premiumPeriodMap);

            pricingDate = discountedPeriodStartDate;
        }

        Double discountedPeriodPrice = getDiscountedMonthlyPayment();

        Map discountedPeriodMap = new HashMap();
        discountedPeriodMap.put("startDate", pricingDate.toDate());
        discountedPeriodMap.put("expiryDate", pricingStartDate.plusMonths(switchingNationalityService.getDefaultPaymentsDuration(contract) - 1).dayOfMonth().withMaximumValue().toDate());
        discountedPeriodMap.put("price", discountedPeriodPrice);

        prices.put("discountedPeriod", discountedPeriodMap);

        return prices;
    }
    
    public List<ContractPaymentType> getContractPaymentTypes() {
        return contractPaymentTypes;
    }

    public void setContractPaymentTypes(List<ContractPaymentType> contractPaymentTypes) {
        this.contractPaymentTypes = contractPaymentTypes;
        if (this.contractPaymentTypes != null) {
            for (ContractPaymentType contractPaymentType : this.contractPaymentTypes) {
                contractPaymentType.setContractPaymentTerm(this);
            }
        }
    }

    @Override
    List<AbstractPaymentTypeConfig> getPaymentTypeConfigs(String paymentTypeCode) {
        List<AbstractPaymentTypeConfig> paymentTypeConfigs = null;
        if (getContractPaymentTypes() != null) {
            paymentTypeConfigs = this.getContractPaymentTypes().stream()
                    .filter(item -> item.getType().getCode().equals(paymentTypeCode))
                    .collect(Collectors.toList());
        }
        return paymentTypeConfigs != null && !paymentTypeConfigs.isEmpty() ?
                paymentTypeConfigs : null;
    }

    @JsonIgnore
    public PaymentTermConfig getNewTermConfig() {
        PaymentTermConfig termConfig = new PaymentTermConfig();

        termConfig.setId(this.getPaymentTermConfig().getId());
        termConfig.setNationality(this.getNationality());
        termConfig.setContractProspectType(this.getContractProspectType());
        termConfig.setType(this.getType());
        termConfig.setPackageType(this.getPackageType());
        termConfig.setIsRemote(this.getIsRemote());
        termConfig.setPeriodicalAdditionalDiscount(this.getPeriodicalAdditionalDiscount());
        termConfig.setWeeklyAmount(this.getWeeklyAmount());
        termConfig.setDailyRateAmount(this.getDailyRateAmount());
        termConfig.setCptFamily(getCptFamily());

        List<PaymentTypeConfig> paymentTypeConfigs = this.getContractPaymentTypes()
                .stream()
                .map(contractPaymentType -> new PaymentTypeConfig(contractPaymentType))
                .collect(Collectors.toList());

        termConfig.setPaymentTypeConfigs(paymentTypeConfigs);

        return termConfig;
    }

    public Date getAmendedDate() {
        return amendedDate;
    }

    public void setAmendedDate(Date amendedDate) {
        this.amendedDate = amendedDate;
    }

    public WordTemplate getTaxInvoiceTemplate() {
        return getWeeklyAmount() > 0.0 ?
                getPaymentTermConfig().getWeeklyTaxInvoiceTemplate() : getPaymentTermConfig().getTaxInvoiceTemplate();
    }

    public WordTemplate getPaymentTermsTemplate() {

        return getWeeklyAmount() > 0.0 ?
                getPaymentTermConfig().getWeeklyPaymentTermsTemplate() :
                getPaymentTermConfig().getPaymentTermsTemplate();
    }

    @JsonIgnore
    public AbstractPaymentTypeConfig getMonthlyPaymentType() {
        return getPaymentTypeConfig(AbstractPaymentTypeConfig.MONTHLY_PAYMENT_TYPE_CODE);
    }

    public boolean getOverrideSwitchNationalityValidation() {
        return overrideSwitchNationalityValidation != null && overrideSwitchNationalityValidation;
    }

    public void setOverrideSwitchNationalityValidation(Boolean overrideSwitchNationalityValidation) {
        this.overrideSwitchNationalityValidation = overrideSwitchNationalityValidation;
    }

    @JsonIgnore
    public boolean isForceGenerateDds() { return forceGenerateDds; }

    public void setForceGenerateDds(boolean forceGenerateDds) { this.forceGenerateDds = forceGenerateDds; }

    public Double getCreditNote() { return creditNote; }

    public void setCreditNote(Double creditNote) { this.creditNote = creditNote; }

    public Integer getCreditNoteMonths() { return creditNoteMonths; }

    public void setCreditNoteMonths(Integer creditNoteMonths) { this.creditNoteMonths = creditNoteMonths; }

    @JsonIgnore
    public List<ContractPaymentTermDetails> getContractPaymentTermDetails() {
        if (contractPaymentTermDetails != null) return contractPaymentTermDetails;

        setContractPaymentTermDetails(Setup.getApplicationContext()
                .getBean(ContractPaymentTermDetailsService.class)
                .getContractPaymentTermDetails(this));

        return contractPaymentTermDetails;
    }

    public void setContractPaymentTermDetails(List<ContractPaymentTermDetails> contractPaymentTermDetails) {
        this.contractPaymentTermDetails = contractPaymentTermDetails;
    }

    public String getSourceId() { return sourceId; }

    public void setSourceId(String sourceId) { this.sourceId = sourceId; }

    public String getSourceInfoAsString() {return sourceInfo;}

    public Map<String, Object> getSourceInfo() {
        Map<String, Object> sourceInfoMap = new HashMap<>();
        if (sourceInfo == null || sourceInfo.isEmpty()) return sourceInfoMap;

        sourceInfo = sourceInfo.replaceAll(",\\s*\\}", "}");

        ObjectMapper objectMapper = Setup.getApplicationContext().getBean(ObjectMapper.class);
        try {
            sourceInfoMap = objectMapper.readValue(sourceInfo, new TypeReference<Map<String, Object>>(){} );
            LocalDate expiryLocalDate = new LocalDate()
                    .withYear((int) sourceInfoMap.get("expiry_year"))
                    .withMonthOfYear((int) sourceInfoMap.get("expiry_month"))
                    .dayOfMonth().withMaximumValue();

            sourceInfoMap.put("expiryDate", expiryLocalDate);
            sourceInfoMap.put("expiryDateFormatted", expiryLocalDate.toString("yyyy-MM-dd"));
            sourceInfoMap.put("cardNumber", "**** **** **** " + sourceInfoMap.get("last4"));

        } catch (Exception e) {
            e.printStackTrace();
        }

        return sourceInfoMap;
    }
    public void setSourceInfo(String sourceInfo) { this.sourceInfo = sourceInfo; }

    public Double getSourceAmount() { return sourceAmount; }

    public void setSourceAmount(Double sourceAmount) { this.sourceAmount = sourceAmount; }

    public Long getDdcId() { return ddcId; }

    public void setDdcId(Long ddcId) { this.ddcId = ddcId; }
}