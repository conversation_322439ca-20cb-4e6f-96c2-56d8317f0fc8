package com.magnamedia.entity.dto;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Setter
@Getter
public class ContractPaymentConfirmationToDoFromGPTDto {
    private Long contractId;
    private String paymentMethod;
    private boolean payingOnline = true;
    private String newPaymentDate;
    private String newPaymentIsProrated = "false";
    private Date parsedDate;

    public boolean isNewPaymentProrated() {
        return Boolean.parseBoolean(newPaymentIsProrated);
    }
}