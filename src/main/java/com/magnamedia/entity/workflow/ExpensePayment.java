package com.magnamedia.entity.workflow;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.AfterInsert;
import com.magnamedia.core.annotation.AfterUpdate;
import com.magnamedia.core.annotation.BeforeInsert;
import com.magnamedia.core.annotation.BeforeUpdate;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.entity.User;
import com.magnamedia.core.entity.workflow.WorkflowEntity;
import com.magnamedia.core.imc.InterModuleConnector;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.core.workflow.FormField;
import com.magnamedia.entity.*;
import com.magnamedia.entity.serializer.ExpenseIdLabelCodeCaptionSerializer;
import com.magnamedia.entity.serializer.ExpensePaymentTransactionSerializer;
import com.magnamedia.entity.serializer.ExpenseRequestForPaymentSerializer;
import com.magnamedia.entity.serializer.IdNameSerializer;
import com.magnamedia.helper.PicklistHelper;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.*;
import com.magnamedia.repository.*;
import com.magnamedia.service.CurrencyExchangeSevice;
import com.magnamedia.service.ExpensePaymentService;
import com.magnamedia.workflow.type.ExpenseRequestStatus;
import org.apache.commons.lang3.BooleanUtils;

import javax.persistence.*;
import java.util.*;
import java.util.function.Function;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Mohammad Nosairat (Jan 18, 2021)
 */
@Entity
public class ExpensePayment extends WorkflowEntity {

    @Enumerated(value = EnumType.STRING)
    private ExpensePaymentMethod method;
    @Enumerated(value = EnumType.STRING)
    private ExpensePaymentStatus status;
    @Enumerated(value = EnumType.STRING)
    private ExpensePaymentType type = ExpensePaymentType.PAY;

    private Double amount;

    @Column(columnDefinition = "boolean default true")
    private Boolean requiresInvoice;
    private Boolean missingVatInvoice;
    
    @Lob
    private String description;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdNameSerializer.class)
    private Bucket fromBucket;


    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdNameSerializer.class)
    private Bucket toBucket;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = ExpensePaymentTransactionSerializer.class)
    private Transaction transaction;
    
    @Enumerated(EnumType.STRING)
    private ExpenseBeneficiaryType beneficiaryType;

    private Long beneficiaryId;

    @Column(columnDefinition = "boolean default false")
    private Boolean confirmed = Boolean.FALSE;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private PicklistItem currency;

    private Double localCurrencyAmount;

    private Double amountToPay;

    private Double loanAmount;

//    @ManyToOne(fetch = FetchType.LAZY)
//    @JsonSerialize(using = IdLabelSerializer.class)
//    private ExpenseRelatedTo relatedTo;
//
//    private Long relatedToId;

    @Column
    private Long relatedToId;

    @Column
    @Enumerated(EnumType.STRING)
    private ExpenseRelatedTo.ExpenseRelatedToType relatedToType;

    @Lob
    private String Instructions;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private User requester;

    @Lob
    private String approvedBy;

    @Lob
    private String notes;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = ExpenseIdLabelCodeCaptionSerializer.class)
    private Expense expenseToPost;

    private Date fromDate;

    private Date toDate;

    private Boolean taxable;

    private Double vatAmount;

    private String invoiceNumber;
    private String beneficiaryName;
    private String beneficiaryIBAN;
    private String beneficiaryAccountNumber;
    private String beneficiaryAccountName;
    
    @Lob
    private String beneficiaryAddress;
    private String beneficiaryEidCopy;
    private String beneficiaryMobileNumber;
    private Boolean beneficiaryInternational;
    private String beneficiarySwift;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private User paidBy;

    private Date paymentDate;

    private Double transferCharges;

    @OneToMany(mappedBy = "expensePayment")
    @JsonSerialize(using = ExpenseRequestForPaymentSerializer.class)
    private List<ExpenseRequestTodo> expenseRequestTodos = new ArrayList<>();


    //    private Boolean invoiceContainsVat;
    private Boolean attachedValidVatInvoice;

    private boolean businessAfterPaidDone;
    private boolean doneInPendingInvoiceStep;
    private Boolean invoiceAttached;

    @ManyToOne
    @JsonSerialize(using = IdLabelSerializer.class)
    private BucketReplenishmentTodo replenishmentTodo;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private PicklistItem salaryAdditionType;

    //Jirra ACC-3315
    @Column(columnDefinition = "boolean default false")
    private boolean doneByCoo = Boolean.FALSE;

    //Jirra ACC-3315
    @Column(columnDefinition = "boolean default false")
    private boolean modifiedByReconciliator = Boolean.FALSE;

    @Transient
    private boolean callUpdateAmountsTrigger = Boolean.TRUE;

    public boolean isDoneInPendingInvoiceStep() {
        return doneInPendingInvoiceStep;
    }

    public void setDoneInPendingInvoiceStep(boolean doneInPendingInvoiceStep) {
        this.doneInPendingInvoiceStep = doneInPendingInvoiceStep;
    }

    public boolean isBusinessAfterPaidDone() {
        return businessAfterPaidDone;
    }

    public void setBusinessAfterPaidDone(boolean businessAfterPaidDone) {
        this.businessAfterPaidDone = businessAfterPaidDone;
    }

    public PicklistItem getSalaryAdditionType() {
        return salaryAdditionType;
    }

    public void setSalaryAdditionType(PicklistItem salaryAdditionType) {
        this.salaryAdditionType = salaryAdditionType;
    }

    public BucketReplenishmentTodo getReplenishmentTodo() {
        return replenishmentTodo;
    }

    public void setReplenishmentTodo(BucketReplenishmentTodo replenishmentTodo) {
        this.replenishmentTodo = replenishmentTodo;
    }

    public Bucket getToBucket() {
        return toBucket;
    }

    public void setToBucket(Bucket toBucket) {
        this.toBucket = toBucket;
    }

    public Boolean isInvoiceAttached() {
        return invoiceAttached;
    }

    public void setInvoiceAttached(Boolean invoiceAttached) {
        this.invoiceAttached = invoiceAttached;
    }

//    public Boolean getInvoiceContainsVat() {
//        return invoiceContainsVat;
//    }
//
//    public void setInvoiceContainsVat(Boolean invoiceContainsVat) {
//        this.invoiceContainsVat = invoiceContainsVat;
//    }

    public Boolean getAttachedValidVatInvoice() {
        return attachedValidVatInvoice;
    }

    public Boolean getMissingVatInvoice() {
        return missingVatInvoice;
    }

    public void setMissingVatInvoice(Boolean missingVatInvoice) {
        this.missingVatInvoice = missingVatInvoice;
    }

    public void setAttachedValidVatInvoice(Boolean attachedValidVatInvoice) {
        this.attachedValidVatInvoice = attachedValidVatInvoice;
    }

    public ExpensePayment(String startTaskName) {
        super(startTaskName);
    }

    public ExpensePayment() {
        super("");
    }

    public List<ExpenseRequestTodo> getExpenseRequestTodos() {
        return expenseRequestTodos;
    }

    public void setExpenseRequestTodos(List<ExpenseRequestTodo> expenseRequestTodos) {
        this.expenseRequestTodos = expenseRequestTodos;
    }

    @Override
    public String getFinishedTaskName() {
        return "";
    }

    @Override
    public List<FormField> getForm(String s) {
        return null;
    }


    public ExpensePaymentMethod getMethod() {
        return method;
    }

    public void setMethod(ExpensePaymentMethod method) {
        this.method = method;
    }

    public ExpensePaymentStatus getStatus() {
        return status;
    }

    public void setStatus(ExpensePaymentStatus status) {
        this.status = status;
    }

    public ExpensePaymentType getType() {
        return type;
    }

    public void setType(ExpensePaymentType type) {
        this.type = type;
    }

    public Double getAmount() {
        return amount;
    }

    public void setAmount(Double amount) {
        this.amount = amount;
    }

    public Boolean getRequiresInvoice() {
        return requiresInvoice != null && requiresInvoice;
    }

    public void setRequiresInvoice(Boolean requiresInvoice) {
        this.requiresInvoice = requiresInvoice;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Bucket getFromBucket() {
        return fromBucket;
    }

    public void setFromBucket(Bucket fromBucket) {
        this.fromBucket = fromBucket;
    }

    public Boolean getConfirmed() {
        return confirmed;
    }

    public void setConfirmed(Boolean confirmed) {
        this.confirmed = confirmed;
    }


    public Double getLocalCurrencyAmount() {
        return localCurrencyAmount;
    }

    public void setLocalCurrencyAmount(Double localCurrencyAmount) {
        this.localCurrencyAmount = localCurrencyAmount;
    }

    public Double getAmountToPay() {
        return amountToPay;
    }

    public void setAmountToPay(Double amountToPay) {
        this.amountToPay = amountToPay;
    }

    public Double getLoanAmount() {
        return loanAmount;
    }

    public void setLoanAmount(Double loanAmount) {
        this.loanAmount = loanAmount;
    }

    public ExpenseRelatedTo.ExpenseRelatedToType getRelatedToType() {
        return relatedToType;
    }

    public void setRelatedToType(ExpenseRelatedTo.ExpenseRelatedToType relatedToType) {
        this.relatedToType = relatedToType;
    }

    public Long getRelatedToId() {
        return relatedToId;
    }

    public void setRelatedToId(Long relatedToId) {
        this.relatedToId = relatedToId;
    }

    public String getRelatedToName() {
        return Setup.getApplicationContext().getBean(ExpensePaymentService.class)
                .getRelatedToName(this.relatedToType, this.relatedToId);
    }

    public String getInstructions() {
        return Instructions;
    }

    public void setInstructions(String instructions) {
        Instructions = instructions;
    }

    public String getApprovedBy() {
        return approvedBy;
    }

    public void setApprovedBy(String approvedBy) {
        this.approvedBy = approvedBy;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public Date getFromDate() {
        return fromDate;
    }

    public void setFromDate(Date fromDate) {
        this.fromDate = fromDate;
    }

    public Date getToDate() {
        return toDate;
    }

    public void setToDate(Date toDate) {
        this.toDate = toDate;
    }

    public Boolean getTaxable() {
        return taxable;
    }

    public void setTaxable(Boolean taxable) {
        this.taxable = taxable;
    }

    public Double getVatAmount() {
        return vatAmount;
    }

    public void setVatAmount(Double vatAmount) {
        this.vatAmount = vatAmount;
    }

    public String getInvoiceNumber() {
        return invoiceNumber;
    }

    public void setInvoiceNumber(String invoiceNumber) {
        this.invoiceNumber = invoiceNumber;
    }

    @Transient
    private Housemaid beneficiaryHousemaid;
    @Transient
    private OfficeStaff beneficiaryOfficeStaff;
    @Transient
    private Supplier beneficiarySupplier;

    private String getBeneficiaryHousemaidValue(String defaultValue, Function<Housemaid, String> exp) {
        if (exp == null) return defaultValue;
        if (beneficiaryHousemaid == null) {
            beneficiaryHousemaid = Setup.getRepository(HousemaidRepository.class).getOne(beneficiaryId);
        }
        if (beneficiaryHousemaid != null)
            return exp.apply(beneficiaryHousemaid);
        return defaultValue;
    }

    private PicklistItem getBeneficiaryHousemaidNationalityValue(Function<Housemaid, PicklistItem> exp) {
        if (exp == null) return null;

        if (beneficiaryHousemaid == null) {
            beneficiaryHousemaid = Setup.getRepository(HousemaidRepository.class).getOne(beneficiaryId);
        }

        if (beneficiaryHousemaid != null)
            return exp.apply(beneficiaryHousemaid);

        return null;
    }

    private String getBeneficiaryOfficeStaffValue(String defaultValue, Function<OfficeStaff, String> exp) {
        if (exp == null) return defaultValue;
        if (beneficiaryOfficeStaff == null) {
            beneficiaryOfficeStaff = Setup.getRepository(OfficeStaffRepository.class).getOne(beneficiaryId);
        }
        if (beneficiaryOfficeStaff != null)
            return exp.apply(beneficiaryOfficeStaff);
        return defaultValue;
    }

    private String getBeneficiarySupplierValue(String defaultValue, Function<Supplier, String> exp) {
        if (exp == null) return defaultValue;
        if (beneficiarySupplier == null) {
            beneficiarySupplier = Setup.getRepository(SupplierRepository.class).getOne(beneficiaryId);
        }
        if (beneficiarySupplier != null) {
            Logger.getLogger(ExpensePayment.class.getName()).log(Level.SEVERE, "ExpensePayment: beneficiarySupplier.getAccountName() " + beneficiarySupplier.getAccountName());
            Logger.getLogger(ExpensePayment.class.getName()).log(Level.SEVERE, "ExpensePayment: exp.apply(beneficiarySupplier) " + exp.apply(beneficiarySupplier));
            return exp.apply(beneficiarySupplier);
        }
        return defaultValue;
    }

    private Boolean getBeneficiarySupplierValueBoolean(Boolean defaultValue, Function<Supplier, Boolean> exp) {
        if (exp == null) return defaultValue;
        if (beneficiarySupplier == null) {
            beneficiarySupplier = Setup.getRepository(SupplierRepository.class).getOne(beneficiaryId);
        }
        if (beneficiarySupplier != null) {
            Logger.getLogger(ExpensePayment.class.getName()).log(Level.SEVERE, "ExpensePayment: beneficiarySupplier.getAccountName() " + beneficiarySupplier.getAccountName());
            Logger.getLogger(ExpensePayment.class.getName()).log(Level.SEVERE, "ExpensePayment: exp.apply(beneficiarySupplier) " + exp.apply(beneficiarySupplier));
            return exp.apply(beneficiarySupplier);
        }
        return defaultValue;
    }

    public String getBeneficiaryName() {
        if (beneficiaryType != null) {
            switch (beneficiaryType) {
                case MAID: {
                    return getBeneficiaryHousemaidValue(this.beneficiaryName, Housemaid::getName);
                }
                case OFFICE_STAFF: {
                    return getBeneficiaryOfficeStaffValue(this.beneficiaryName, OfficeStaff::getNameDestination);
                }
                case SUPPLIER: {
                    return getBeneficiarySupplierValue(this.beneficiaryName, Supplier::getName);
                }
                case TAXI_DRIVER: {
                    return "Taxi Driver";
                }
            }
        }
        return beneficiaryName;
    }

    public PicklistItem getBeneficiaryNationality() {
        PicklistItem beneficiaryNationality = null;

        if (beneficiaryType != null) {
            switch (beneficiaryType) {
                case MAID: {
                    beneficiaryNationality = getBeneficiaryHousemaidNationalityValue(Housemaid::getNationality);
                }
            }
        }

        return beneficiaryNationality;
    }

    @JsonIgnore
    public Map<?, ?> getBeneficiaryExtraDetails() {
        Map extraDetails = new HashMap();
        PayrollManagerNote payrollManagerNote = null;

        if (beneficiaryType != null) {
            switch (beneficiaryType) {
                case MAID: {
                    if (beneficiaryHousemaid == null) {
                        beneficiaryHousemaid = Setup.getRepository(HousemaidRepository.class).getOne(beneficiaryId);
                    }

                    if (beneficiaryHousemaid != null) {
                        payrollManagerNote = Setup.getRepository(PayrollManagerNoteRepository.class).findFirstByHousemaidAndNoteTypeOrderByCreationDateDesc(beneficiaryHousemaid, PayrollManagerNote.ManagerNoteType.ADDITION);
                    }
                    break;
                }
                case OFFICE_STAFF: {
                    if (beneficiaryOfficeStaff == null) {
                        beneficiaryOfficeStaff = Setup.getRepository(OfficeStaffRepository.class).getOne(beneficiaryId);
                    }

                    if (beneficiaryOfficeStaff != null) {
                        payrollManagerNote = Setup.getRepository(PayrollManagerNoteRepository.class).findFirstByOfficeStaffAndNoteTypeOrderByCreationDateDesc(beneficiaryOfficeStaff, PayrollManagerNote.ManagerNoteType.ADDITION);
                    }
                    break;
                }
            }
        }

        if (payrollManagerNote != null) {
            extraDetails.put("lastAdditionNote", getBeneficiaryLastAdditionDetails(payrollManagerNote));
        }

        return extraDetails;
    }

    @JsonIgnore
    private Map getBeneficiaryLastAdditionDetails(PayrollManagerNote payrollManagerNote) {
        Map payrollManagerNoteMap = new HashMap();
        payrollManagerNoteMap.put("id", payrollManagerNote.getId());
        payrollManagerNoteMap.put("noteDate", payrollManagerNote.getNoteDate());
        payrollManagerNoteMap.put("amount", payrollManagerNote.getAmount());

        return payrollManagerNoteMap;
    }

    public Date getBeneficiaryStartDate() {
        Date beneficiaryStartDate = null;

        if (beneficiaryType != null) {
            switch (beneficiaryType) {
                case MAID: {
                    if (beneficiaryHousemaid == null) {
                        beneficiaryHousemaid = Setup.getRepository(HousemaidRepository.class).getOne(beneficiaryId);
                    }
                    if (beneficiaryHousemaid != null)
                        beneficiaryStartDate = beneficiaryHousemaid.getStartDate();
                    break;
                }
                case OFFICE_STAFF: {

                    if (beneficiaryOfficeStaff == null) {
                        beneficiaryOfficeStaff = Setup.getRepository(OfficeStaffRepository.class).getOne(beneficiaryId);
                    }
                    if (beneficiaryOfficeStaff != null)
                        beneficiaryStartDate = beneficiaryOfficeStaff.getStartingDate();
                    break;
                }
            }
        }

        return beneficiaryStartDate;
    }

    public void setBeneficiaryName(String beneficiaryName) {
        this.beneficiaryName = beneficiaryName;
    }

    public String getBeneficiaryIBAN() {
        if (beneficiaryType != null) {
            switch (beneficiaryType) {
                case MAID: {
                    return getBeneficiaryHousemaidValue(this.beneficiaryIBAN, null);
                }
                case OFFICE_STAFF: {
                    return getBeneficiaryOfficeStaffValue(this.beneficiaryIBAN, OfficeStaff::getIbanDestination);
                }
                case SUPPLIER: {
                    return getBeneficiarySupplierValue(this.beneficiaryIBAN, Supplier::getIban);
                }
            }
        }

        return beneficiaryIBAN;
    }

    public void setBeneficiaryIBAN(String beneficiaryIBAN) {
        this.beneficiaryIBAN = beneficiaryIBAN;
    }

    public Boolean getBeneficiaryInternational() {

        if (beneficiaryType != null && beneficiaryType.equals(ExpenseBeneficiaryType.SUPPLIER)) {
            return getBeneficiarySupplierValueBoolean(this.beneficiaryInternational, Supplier::getInternational);
        }
        return beneficiaryInternational;
    }

    public void setBeneficiaryInternational(Boolean beneficiaryInternational) {
        this.beneficiaryInternational = beneficiaryInternational;
    }

    public String getBeneficiarySwift() {

        if (beneficiaryType != null && beneficiaryType.equals(ExpenseBeneficiaryType.SUPPLIER)) {
            return getBeneficiarySupplierValue(this.beneficiarySwift, Supplier::getSwift);
        }
        if (beneficiaryType != null && beneficiaryType.equals(ExpenseBeneficiaryType.OFFICE_STAFF)) {
            return getBeneficiaryOfficeStaffValue(this.beneficiarySwift, OfficeStaff::getSwiftDestination);
        }
        return beneficiarySwift;
    }

    public void setBeneficiarySwift(String beneficiarySwift) {
        this.beneficiarySwift = beneficiarySwift;
    }

    public String getBeneficiaryAccountNumber() {
        if (beneficiaryType != null) {
            switch (beneficiaryType) {
                case MAID: {
                    return getBeneficiaryHousemaidValue(this.beneficiaryAccountNumber, null);
                }
                case OFFICE_STAFF: {
                    return getBeneficiaryOfficeStaffValue(this.beneficiaryAccountNumber, OfficeStaff::getAccountNumberDestination);
                }
                case SUPPLIER: {
                    return getBeneficiarySupplierValue(this.beneficiaryAccountNumber, Supplier::getAccountNumber);
                }
            }
        }

        return beneficiaryAccountNumber;
    }

    public void setBeneficiaryAccountNumber(String beneficiaryAccountNumber) {
        this.beneficiaryAccountNumber = beneficiaryAccountNumber;
    }

    public String getBeneficiaryAccountName() {
        if (beneficiaryType != null) {
            switch (beneficiaryType) {
                case MAID: {
                    return getBeneficiaryHousemaidValue(this.beneficiaryAccountName, null);
                }
                case OFFICE_STAFF: {
                    return getBeneficiaryOfficeStaffValue(this.beneficiaryAccountName, OfficeStaff::getAccountNameDestination);
                }
                case SUPPLIER: {
                    Logger.getLogger(ExpensePayment.class.getName()).log(Level.SEVERE, "SUPPLIER");
                    return getBeneficiarySupplierValue(this.beneficiaryAccountName, Supplier::getAccountName);
                }
            }
        }
        return beneficiaryAccountName;
    }

    public void setBeneficiaryAccountName(String beneficiaryAccountName) {
        this.beneficiaryAccountName = beneficiaryAccountName;
    }

    public String getBeneficiaryAddress() {
        if (beneficiaryType != null) {
            switch (beneficiaryType) {
                case MAID: {
                    return getBeneficiaryHousemaidValue(this.beneficiaryAddress, null);
                }
                case OFFICE_STAFF: {
                    return getBeneficiaryOfficeStaffValue(this.beneficiaryAddress, OfficeStaff::getFullAddressDestination);
                }
                case SUPPLIER: {
                    return getBeneficiarySupplierValue(this.beneficiaryAddress, Supplier::getAddress);
                }
            }
        }
        return beneficiaryAddress;
    }

    public void setBeneficiaryAddress(String beneficiaryAddress) {
        this.beneficiaryAddress = beneficiaryAddress;
    }

    public String getBeneficiaryEidCopy() {
        if (beneficiaryType != null) {
            switch (beneficiaryType) {
                case MAID: {
                    return getBeneficiaryHousemaidValue(this.beneficiaryEidCopy, null);
                }
                case OFFICE_STAFF: {
                    return getBeneficiaryOfficeStaffValue(this.beneficiaryEidCopy, OfficeStaff::getEidNumber);
                }
                case SUPPLIER: {
                    return getBeneficiarySupplierValue(this.beneficiaryEidCopy, null);
                }
            }
        }
        return beneficiaryEidCopy;
    }

    public void setBeneficiaryEidCopy(String beneficiaryEidCopy) {
        this.beneficiaryEidCopy = beneficiaryEidCopy;
    }

    public String getBeneficiaryMobileNumber() {
        if (beneficiaryType != null) {
            switch (beneficiaryType) {
                case MAID: {
                    return getBeneficiaryHousemaidValue(this.beneficiaryMobileNumber, Housemaid::getPhoneNumber);
                }
                case OFFICE_STAFF: {
                    return getBeneficiaryOfficeStaffValue(this.beneficiaryMobileNumber, OfficeStaff::getPhoneNumber);
                }
                case SUPPLIER: {
                    return getBeneficiarySupplierValue(this.beneficiaryMobileNumber, Supplier::getMobileNumber);
                }
            }
        }
        return beneficiaryMobileNumber;
    }

    public void setBeneficiaryMobileNumber(String beneficiaryMobileNumber) {
        this.beneficiaryMobileNumber = beneficiaryMobileNumber;
    }

    public User getPaidBy() {
        return paidBy;
    }

    public void setPaidBy(User paidBy) {
        this.paidBy = paidBy;
    }

    public Date getPaymentDate() {
        return paymentDate;
    }

    public void setPaymentDate(Date paymentDate) {
        this.paymentDate = paymentDate;
    }

    public Double getTransferCharges() {
        return transferCharges;
    }

    public void setTransferCharges(Double transferCharges) {
        this.transferCharges = transferCharges;
    }

    public Long getBeneficiaryId() {
        return beneficiaryId;
    }

    public void setBeneficiaryId(Long beneficiaryId) {
        this.beneficiaryId = beneficiaryId;
    }

    public PicklistItem getCurrency() {
        return currency;
    }

    public void setCurrency(PicklistItem currency) {
        this.currency = currency;
    }

    public User getRequester() {
        return requester;
    }

    public void setRequester(User requester) {
        this.requester = requester;
    }

    public Expense getExpenseToPost() {
        return expenseToPost;
    }

    public void setExpenseToPost(Expense expenseToPost) {
        this.expenseToPost = expenseToPost;
    }

    public ExpenseBeneficiaryType getBeneficiaryType() {
        return beneficiaryType;
    }

    public void setBeneficiaryType(ExpenseBeneficiaryType beneficiaryType) {
        this.beneficiaryType = beneficiaryType;
    }

    public Boolean getInvoiceAttached() {
        return invoiceAttached;
    }

    public boolean isDoneByCoo() {
        return doneByCoo;
    }

    public void setDoneByCoo(boolean doneByCoo) {
        this.doneByCoo = doneByCoo;
    }

    public boolean isModifiedByReconciliator() {
        return modifiedByReconciliator;
    }

    public void setModifiedByReconciliator(boolean modifiedByReconciliator) {
        this.modifiedByReconciliator = modifiedByReconciliator;
    }

    public boolean isCallUpdateAmountsTrigger() {
        return callUpdateAmountsTrigger;
    }

    public void setCallUpdateAmountsTrigger(boolean callUpdateAmountsTrigger) {
        this.callUpdateAmountsTrigger = callUpdateAmountsTrigger;
    }

    @JsonIgnore
    public ExpenseRequestType getExpenseRequestType() {
        if (expenseRequestTodos == null || expenseRequestTodos.size() == 0)
            return null;

        return expenseRequestTodos.get(0).getExpenseRequestType();
    }

    public Transaction getTransaction() {
        return transaction;
    }

    public void setTransaction(Transaction transaction) {
        this.transaction = transaction;
    }

    public List<Transaction> getTransactions() {
        return Setup.getRepository(TransactionRepository.class)
                .findByExpensePaymentId(this.getId());
    }

    @JsonIgnore
    public boolean isRelatedToDoConfirmed() {
        if (this.method == null) return false;

        if (this.method.equals(ExpensePaymentMethod.BANK_TRANSFER)) {
            return this.expenseRequestTodos != null && !this.expenseRequestTodos.isEmpty() &&
                    BooleanUtils.toBoolean(this.expenseRequestTodos.get(0).getRefundConfirmed());
        } else if (this.method.equals(ExpensePaymentMethod.MONEY_TRANSFER)) {
            return this.replenishmentTodo != null && BooleanUtils.toBoolean(this.replenishmentTodo.isConfirmed());
        }

        return false;
    }

    @AfterInsert
    public void afterInsertTrigger() {
        afterPersistingTrigger();
        this.updateDescription();
    }

    @AfterUpdate
    public void businessAfterPaymentPaid() {
        afterPersistingTrigger();
        updateModifiedByReconciliatorPropAfterPersist();
    }

    private void updateModifiedByReconciliatorPropAfterPersist() {
        Logger.getLogger(ExpensePayment.class.getName()).info("updateModifiedByReconciliatorPropAfterPersist");
        Logger.getLogger(ExpensePayment.class.getName()).info("isModifiedByReconciliator: " + this.isModifiedByReconciliator());

        if (!BooleanUtils.toBoolean(this.isModifiedByReconciliator())) return;

        InterModuleConnector moduleConnector = Setup.getApplicationContext().getBean(InterModuleConnector.class);

        Map body = new HashMap();
        body.put("id", this.getId());
        body.put("modifiedByReconciliator", Boolean.FALSE);

        moduleConnector.postJsonAsync("accounting/expense-payment/update", body);
    }

    private void afterPersistingTrigger() {
        try {
            if (paymentDate == null) paymentDate = this.getCreationDate();
            if (status == null) return;
            if (businessAfterPaidDone) return;
            if (status.equals(ExpensePaymentStatus.PAID)
                    || status.equals(ExpensePaymentStatus.PAID_PENDING_INVOICE)) {
                businessAfterPaidDone = true;
                Setup.getRepository(ExpensePaymentRepository.class).save(this);
                for (ExpenseRequestTodo todo : expenseRequestTodos) {
                    if (todo.getStatus() == null)
                        todo = Setup.getRepository(ExpenseRequestTodoRepository.class).findOne(todo.getId());
                    todo.setStatus(ExpenseRequestStatus.PAID);
                    Setup.getRepository(ExpenseRequestTodoRepository.class).save(todo);
                    Setup.getApplicationContext().getBean(ExpensePaymentService.class)
                            .businessOnRequestAfterPaymentIsPaid(todo);
                }
                Setup.getApplicationContext().getBean(ExpensePaymentService.class)
                        .businessOnPaymentAfterPaymentIsPaid(this);
            }
        } catch (Exception ex) {
            throw new RuntimeException(ex);
        }
    }

    public void updateExpensesStatus(ExpenseRequestStatus status) {
        List<ExpenseRequestTodo> requests = this.getExpenseRequestTodos();
        for (ExpenseRequestTodo todo : requests) {
            todo.setStatus(status);
            Setup.getRepository(ExpenseRequestTodoRepository.class).save(todo);
        }
    }

    @BeforeInsert
    @BeforeUpdate
    public void setAmountsTrigger() {
        if (callUpdateAmountsTrigger) {
            setAmountsTriggerAndGetLocalCurrencyAmount();
        }
    }

    public Double setAmountsTriggerAndGetLocalCurrencyAmount() {
        String parameter = Setup.getParameter(Setup.getCurrentModule(),
                AccountingModule.EXPENSE_LOCAL_CURRENCY);
        PicklistItem localCurrency = PicklistHelper.getItemNoException(AccountingModule.EXPENSE_CURRENCY, parameter);
        if (currency == null || currency.getId().equals(localCurrency.getId()))
            localCurrencyAmount = amount;
        else {
            CurrencyExchangeSevice currencyExchangeSevice = Setup.getApplicationContext().getBean(CurrencyExchangeSevice.class);
            localCurrencyAmount = currencyExchangeSevice.exchangeToLocal(currency, amount);
        }

        amountToPay = amount;
        //Jirra ACC-3523
        if (modifiedByReconciliator){
            ExpenseRequestTodo expenseRequestTodo =
                    (this.getExpenseRequestTodos() != null && !this.getExpenseRequestTodos().isEmpty()) ?
                                    this.getExpenseRequestTodos().get(0) : null;
            if (expenseRequestTodo != null){
                
                ExpenseRequestTodoRepository repo = Setup.getRepository(ExpenseRequestTodoRepository.class);
                expenseRequestTodo = repo.findOne(expenseRequestTodo.getId());
                expenseRequestTodo.setAmount(amount);
                expenseRequestTodo.setAmountInLocalCurrency(localCurrencyAmount);
                expenseRequestTodo.setAmountToPay(amountToPay);
                expenseRequestTodo.setVatAmount(vatAmount);
                expenseRequestTodo.setLoanAmount(loanAmount);
                repo.save(expenseRequestTodo);
            }
        }
        return localCurrencyAmount;
    }

    private void updateDescription() {
        if (this.replenishmentTodo == null) {
            this.setDescription("Ep" + this.getId() + " " + (this.getDescription() != null ? this.getDescription() : ""));
        }
    }

    @Transient
    private CooQuestion.QuestionedPage cooQuestionedPage;

    public CooQuestion.QuestionedPage getCooQuestionedPage() {
        return cooQuestionedPage;
    }

    public void setCooQuestionedPage(CooQuestion.QuestionedPage cooQuestionedPage) {
        this.cooQuestionedPage = cooQuestionedPage;
    }

    @JsonIgnore
    public List<CooQuestion> getCooQuestions() {
        if (this.cooQuestionedPage == null) return new ArrayList();

        return Setup.getRepository(CooQuestionRepository.class).findByRelatedEntityAndQuestionedPage(this, this.cooQuestionedPage);
    }

    @JsonIgnore
    public boolean isAllQuestionsAnswered() {
        if (this.cooQuestionedPage == null) return false;

        List<CooQuestion> cooQuestions = getCooQuestions();

        return !cooQuestions.isEmpty() && !cooQuestions.stream().anyMatch(q -> !q.isAnswered());
    }

    @JsonIgnore
    public boolean isOneQuestionAnswered() {
        if (this.cooQuestionedPage == null) return false;

        return getCooQuestions().stream().filter(q -> q.isAnswered()).count() >= 1l;
    }

    @JsonIgnore
    public boolean isNoneQuestionAnswered() {
        if (this.cooQuestionedPage == null) return false;

        return getCooQuestions().stream().filter(q -> q.isAnswered()).count() == 0l;
    }

    public String getAmountWithSecure() {

        // ACC-8094
        return Setup.getApplicationContext()
                .getBean(ExpensePaymentService.class)
                .bucketOrExpenseSecureByPageCode(
                    getExpenseToPost() != null && getExpenseToPost().getIsSecure(),
                    getFromBucket() != null && getFromBucket().getIsSecure(),
                    getToBucket() != null && getToBucket().getIsSecure()) ?
                    "***" : amount.toString();
    }
}
