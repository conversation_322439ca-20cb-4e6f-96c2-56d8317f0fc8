package com.magnamedia.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.core.serialize.IdSerializer;
import com.magnamedia.extra.EmployeeType;
import com.magnamedia.extra.ExpenseStatus;
import com.magnamedia.extra.VisaStatementTransactionType;
import com.magnamedia.extra.serializer.IdLabelNameSerializer;
import com.magnamedia.helper.PicklistHelper;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.ContractStatus;
import com.magnamedia.module.type.PaymentType;
import com.magnamedia.repository.*;
import com.magnamedia.service.VisaExpenseService;
import com.magnamedia.workflow.visa.ExpensePurpose;
import org.joda.time.LocalDate;

import javax.persistence.*;
import java.math.BigInteger;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Entity
public class VisaStatementTransaction extends BaseEntity {

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdSerializer.class)
    private VisaStatement statement;

    @Column
    private Long visaRequestExpenseID;

    @Column
    private String visaExpenseType;

    @Transient
    private VisaExpense visaExpense;

    @Enumerated(EnumType.STRING)
    private VisaStatementTransactionType type = VisaStatementTransactionType.MissingFromStatement;

    @Transient
    private boolean isUpdatedExpense;

    @Column
    private Date expenseCreationDate;

    @Column
    private Date rowRecordDate;

    @Column
    private String name;

    @Column
    private String referenceNumber;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private OfficeStaff officeStaff;

    @Column
    @Enumerated(EnumType.STRING)
    private EmployeeType employeeType;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private Housemaid housemaid;

    @Column
    private boolean newEmployee;

    @Column
    @Enumerated(EnumType.STRING)
    private PaymentType paymentType;


    @Column
    @Enumerated(EnumType.STRING)
    private ExpensePurpose purpose;

    @Column
    private double amount;

    @Column
    private Double vatCharge;

    @Column
    private Double charge;

    @Column
    private String equation;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private Transaction transaction;

    @Column
    private String contractType;

    @Column
    private boolean maidVisaAEContract;

    @Column
    @Lob
    private String description;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelNameSerializer.class)
    private Bucket fromBucket;

    @Column
    private Boolean finished = false ;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private Expense expense;

    @Column
    private String amounts;

    @Column
    private String note;


    @Column(columnDefinition = "boolean default false")
    private boolean isCredit = false;



    public VisaStatementTransaction() {
    }

    public VisaStatementTransaction(Object[] records, VisaStatement statement, Double fileAmount) {
        fillVisaStatementTransactionInfo(records, statement, fileAmount);
    }

    public void fillVisaStatementTransactionInfo(Object[] records, VisaStatement statement, Double fileAmount){
        this.statement = statement;
        this.finished = false;
        this.visaRequestExpenseID = (records[0] != null) ? ((BigInteger) records[0]).longValue() : null;
        if (this.visaRequestExpenseID != null) {
            this.visaExpenseType = (records[1] != null) ? (String) records[1] : null;
            this.visaExpense = this.getVisaExpense();
            this.setVisaRequestExpense(fileAmount);
        }
    }

    public void setVisaRequestExpense(Double fileAmount) {
        PicklistItem maidVisa = PicklistHelper.getItem(AccountingModule.PICKLIST_PROSPECTTYPE,
                "maidvisa.ae_prospect");
        this.expenseCreationDate = this.visaExpense.getCreationDate();
        this.name = this.visaExpense.getName();
        this.referenceNumber = this.visaExpense.getReferenceNumber();
        if (this.visaExpense.getRequest() != null) {
            if (this.visaExpense.getRequest().getOfficeStaff() != null) {
                this.officeStaff = this.visaExpense.getRequest().getOfficeStaff();
                this.employeeType = EmployeeType.Officestaff;
            } else if (this.visaExpense.getRequest().getHousemaid() != null) {
                this.housemaid = this.visaExpense.getRequest().getHousemaid();
                this.employeeType = EmployeeType.Housemaid;
            }

            this.newEmployee =
                    this.visaExpense.getRequest().getNewEmployee() != null ?
                            this.visaExpense.getRequest().getNewEmployee() : false;
        }

        this.paymentType = this.visaExpense.getPaymentType();
        this.purpose = this.visaExpense.getPurpose();
        if (fileAmount != null) {
            setAmount(fileAmount);
            setCharge(null);
            setVatCharge(null);
        } else {
            setAmount(getVisaExpense().getAmount());
            setCharge(getVisaExpense().getCharge());
            setVatCharge(getVisaExpense().getVatCharge());
        }
        this.equation = this.visaExpense.getEquation();
        this.transaction = this.visaExpense.getTransaction();
        this.contractType = "";

        if (housemaid != null) {
            PicklistItem contractProspectType = null;

            List<Contract> contracts = Setup.getRepository(ContractRepository.class).findByHousemaidAndStatus(housemaid, ContractStatus.ACTIVE);
            if (contracts.size() > 0) {
                contractProspectType = contracts.get(0).getContractProspectType();
            } else {
                Contract lastContract = Setup.getRepository(ContractRepository.class).findFirstOneByHousemaidOrderByCreationDateDesc(housemaid);
                if (lastContract != null) {
                    contractProspectType = lastContract.getContractProspectType();
                }
            }

            if (contractProspectType == null) {
                this.maidVisaAEContract = false;
                this.contractType = "";
            } else {
                if (maidVisa != null && contractProspectType.getId().equals(maidVisa.getId())) {
                    this.maidVisaAEContract = true;
                    this.contractType = "Maid.Visa";
                } else {
                    this.maidVisaAEContract = false;
                    if (contractProspectType.getCode() != null && contractProspectType.getCode().equals(PicklistItem.getCode(AccountingModule.MAID_CC_PROSPECT_TYPE)))
                        this.contractType = "Maid.cc";
                    else
                        this.contractType = contractProspectType.getName();

                }
            }
        }

        if (this.visaExpense.getStatus() == ExpenseStatus.Added
                && this.visaExpense.getTransaction() != null) {
            this.description = this.visaExpense.getTransaction().getDescription();
            this.fromBucket = this.visaExpense.getTransaction().getFromBucket();
            this.expense = this.visaExpense.getTransaction().getExpense();
        } else {
            this.description =
                    (this.housemaid != null ? this.housemaid.getName() :
                            (this.officeStaff != null ? this.officeStaff.getName() : ""))
                            + " / " + (this.newEmployee ? "New " : "Current ")
                            + (this.employeeType == null ? "" : this.employeeType.toString() + " / ")
                            + (this.purpose != null ? this.purpose.getLabel() : "") +
                            // ACC-7373 ACC-6912 @Existing Description Format@ / @Creation Date@ / @Reference Number@
                            " / " + new LocalDate(getExpenseCreationDate()).toString("yyyy-MM-dd") +
                            (getReferenceNumber() == null ? "" : (" / "  + getReferenceNumber()));
            // ACC-6650
            if (getPaymentType() != null &&
                    getEmployeeType() != null &&
                    getPurpose() != null) {

                VisaExpenseService visaExpenseService = Setup.getApplicationContext()
                        .getBean(VisaExpenseService.class);

                Map<String, Map<String, Object>> defaultFromBuckets = visaExpenseService.getDefaultFromBuckets();
                Object[] expenseCode = visaExpenseService.getDefaultExpense(
                        getPurpose(), getEmployeeType(), isNewEmployee(), isMaidVisaAEContract(), getPaymentType());

                // ACC-7372
                if (getVisaExpense() != null && getVisaExpense().getDefaultBucketCode() != null) {
                    setFromBucket(Setup.getRepository(BucketRepository.class)
                            .findFirstByCode(getVisaExpense().getDefaultBucketCode()));

                } else if (defaultFromBuckets.containsKey(paymentType.toString().toLowerCase()) &&
                        (employeeType.equals(EmployeeType.Housemaid) ||
                                (employeeType.equals(EmployeeType.Officestaff) &&
                                        (expenseCode != null && expenseCode.length > 0)))) {

                    setFromBucket(Setup.getRepository(BucketRepository.class).findOne(
                            (Long) defaultFromBuckets.get(paymentType.toString().toLowerCase()).get("id")));
                }

                if (expenseCode != null && expenseCode.length > 0) {
                    setExpense(Setup.getRepository(ExpenseRepository.class).findOne((Long) expenseCode[1]));
                }
            }
        }
    }

    @JsonIgnore
    public VisaExpense getVisaExpense() {
        if(this.visaExpenseType == null) return null;

        if (this.visaExpenseType.equalsIgnoreCase("CancelRequestExpense")) {
            CancelVisaRequestExpenseRepository repository =
                    Setup.getRepository(CancelVisaRequestExpenseRepository.class);
            return repository.findOne(this.visaRequestExpenseID);
        } else if (this.visaExpenseType.equalsIgnoreCase("NewRequestExpense")) {
            NewVisaRequestExpenseRepository repository =
                    Setup.getRepository(NewVisaRequestExpenseRepository.class);
            return repository.findOne(this.visaRequestExpenseID);
        } else if (this.visaExpenseType.equalsIgnoreCase("RepeatEIDRequestExpense")) {
            RepeatEIDExpenseRepository repository =
                    Setup.getRepository(RepeatEIDExpenseRepository.class);
            return repository.findOne(this.visaRequestExpenseID);
        } else if (this.visaExpenseType.equalsIgnoreCase("RenewRequestExpense")) {
            RenewVisaRequestExpenseRepository repository =
                    Setup.getRepository(RenewVisaRequestExpenseRepository.class);
            return repository.findOne(this.visaRequestExpenseID);
        } else if (this.visaExpenseType.equalsIgnoreCase("ContractModificationExpense")) {
            ContractModificationExpenseRepository repository =
                    Setup.getRepository(ContractModificationExpenseRepository.class);
            return repository.findOne(this.visaRequestExpenseID);
        } else if (this.visaExpenseType.equalsIgnoreCase("ModifyVisaRequestExpense")) {
            ModifyVisaRequestExpenseRepository repository =
                    Setup.getRepository(ModifyVisaRequestExpenseRepository.class);
            return repository.findOne(this.visaRequestExpenseID);
        } else
            return null;
    }


    public VisaStatement getStatement() {
        return statement;
    }

    public void setStatement(VisaStatement statement) {
        this.statement = statement;
    }

    public Long getVisaRequestExpenseID() {
        return visaRequestExpenseID;
    }

    public void setVisaRequestExpenseID(Long visaRequestExpenseID) {
        this.visaRequestExpenseID = visaRequestExpenseID;
    }

    public String getVisaExpenseType() {
        return visaExpenseType;
    }

    public void setVisaExpenseType(String visaExpenseType) {
        this.visaExpenseType = visaExpenseType;
    }

    public void setVisaExpense(VisaExpense visaExpense) {
        this.visaExpense = visaExpense;
    }

    public boolean isUpdatedExpense() {
        return isUpdatedExpense;
    }

    public void setUpdatedExpense(boolean updatedExpense) {
        isUpdatedExpense = updatedExpense;
    }

    public Date getExpenseCreationDate() {
        return expenseCreationDate;
    }

    public void setExpenseCreationDate(Date expenseCreationDate) {
        this.expenseCreationDate = expenseCreationDate;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getReferenceNumber() {
        return referenceNumber;
    }

    public void setReferenceNumber(String referenceNumber) {
        this.referenceNumber = referenceNumber;
    }

    public OfficeStaff getOfficeStaff() {
        return officeStaff;
    }

    public void setOfficeStaff(OfficeStaff officeStaff) {
        this.officeStaff = officeStaff;
    }

    public EmployeeType getEmployeeType() {
        return employeeType;
    }

    public void setEmployeeType(EmployeeType employeeType) {
        this.employeeType = employeeType;
    }

    public Housemaid getHousemaid() {
        return housemaid;
    }

    public void setHousemaid(Housemaid housemaid) {
        this.housemaid = housemaid;
    }

    public boolean isNewEmployee() {
        return newEmployee;
    }

    public void setNewEmployee(boolean newEmployee) {
        this.newEmployee = newEmployee;
    }

    public PaymentType getPaymentType() {
        return paymentType;
    }

    public void setPaymentType(PaymentType paymentType) {
        this.paymentType = paymentType;
    }

    public ExpensePurpose getPurpose() {
        return purpose;
    }

    public void setPurpose(ExpensePurpose purpose) {
        this.purpose = purpose;
    }

    public double getAmount() {
        return amount;
    }

    public void setAmount(double amount) {
        this.amount = amount;
    }

    public Double getVatCharge() {
        return vatCharge;
    }

    public void setVatCharge(Double vatCharge) {
        this.vatCharge = vatCharge;
    }

    public Double getCharge() {
        return charge;
    }

    public void setCharge(Double charge) {
        this.charge = charge;
    }

    public String getEquation() {
        return equation;
    }

    public void setEquation(String equation) {
        this.equation = equation;
    }

    public Transaction getTransaction() {
        return transaction;
    }

    public void setTransaction(Transaction transaction) {
        this.transaction = transaction;
    }

    public String getContractType() {
        return contractType;
    }

    public void setContractType(String contractType) {
        this.contractType = contractType;
    }

    public boolean isMaidVisaAEContract() {
        return maidVisaAEContract;
    }

    public void setMaidVisaAEContract(boolean maidVisaAEContract) {
        this.maidVisaAEContract = maidVisaAEContract;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Bucket getFromBucket() {
        return fromBucket;
    }

    public void setFromBucket(Bucket fromBucket) {
        this.fromBucket = fromBucket;
    }

    public Expense getExpense() {
        return expense;
    }

    public void setExpense(Expense expense) {
        this.expense = expense;
    }

    public Boolean getFinished() {
        return finished;
    }

    public void setFinished(Boolean finished) {
        this.finished = finished;
    }

    public VisaStatementTransactionType getType() {
        return type;
    }

    public void setType(VisaStatementTransactionType type) {
        this.type = type;
    }

    public Date getRowRecordDate() {
        return rowRecordDate;
    }

    public void setRowRecordDate(Date rowRecordDate) {
        this.rowRecordDate = rowRecordDate;
    }

    public String getAmounts() {
        return amounts;
    }

    public void setAmounts(String amounts) {
        this.amounts = amounts;
    }

    public String getNote() {
        return note;
    }

    public void setNote(String note) {
        this.note = note;
    }

    public boolean isCredit() {
        return isCredit;
    }

    public void setCredit(boolean credit) {
        isCredit = credit;
    }

    public void initObj() {
        this.expense = null;
        this.fromBucket = null;
        this.contractType = null;
        this.description = null;
        this.vatCharge = null;
        this.charge = null;
        this.note = null;
        this.maidVisaAEContract = false;
        this.equation = null;
        this.transaction = null;
        this.employeeType = null;
        this.expenseCreationDate = null;
        this.name = null;
        this.newEmployee = false;
        this.purpose = null;
        this.visaExpenseType = null;
        this.visaRequestExpenseID = null;
        this.housemaid = null;
        this.officeStaff = null;
        this.paymentType = null;
        return;
    }

    public Double getErpAmount() {
        VisaExpense e = getVisaExpense();
        return e != null ? e.getAmount() : 0;
    }
}
