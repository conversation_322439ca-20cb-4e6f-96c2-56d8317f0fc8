package com.magnamedia.extra;


/*
 * <AUTHOR>
 * @created 02/07/2024 - 5:07 PM
 * ACC-7640
 */

public enum UploadStatementEntityType {
    OnlineCardStatementFile("onlineCardStatementService", "parseFile"),
    BankStatementFile("bankStatementFileService", "parseFile"),
    BankDirectDebitActivationFile("bankDirectDebitActivationFileController", "parseUploadedFile"),
    BankDirectDebitActivationByRPAFile("bankDirectDebitActivationFileController", "parseUploadedFileByRPA"),
    BankDirectDebitCancelationFile("bankDirectDebitCancellationRecordService", "processFile"),
    BankDirectDebitCancelationFileByRPA("bankDirectDebitCancellationRecordService", "processFileByRPA"),
    VisaStatement("visaStatementService", "createAllVisaTransactions");

    private final String targetBean;
    private final String targetMethod;

    Up<PERSON>StatementEntityType(String targetBean, String targetMethod) {
        this.targetBean = targetBean;
        this.targetMethod = targetMethod;
    }

    public String getTargetBean() { return targetBean; }

    public String getTargetMethod() { return targetMethod; }
}