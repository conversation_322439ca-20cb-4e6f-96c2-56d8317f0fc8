package com.magnamedia.scheduledjobs;

import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.BackgroundTask;
import com.magnamedia.core.helper.BackgroundTaskService;
import com.magnamedia.core.helper.paytabs.PaytabsException;
import com.magnamedia.core.helper.paytabs.PaytabsTransactionInfo;
import com.magnamedia.core.schedule.MagnamediaJob;
import com.magnamedia.core.type.BackgroundTaskQueues;
import com.magnamedia.entity.ContractPaymentConfirmationToDo;
import com.magnamedia.helper.PayTabsHelper;
import com.magnamedia.repository.ContractPaymentConfirmationToDoRepository;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.logging.Level;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Sep 19, 2021
 *         Jirra ACC-3710
 */

public class PayTabTransactionJob implements MagnamediaJob {

    private PayTabsHelper payTabsHelper;
    private BackgroundTaskService backgroundTaskService;
    private ContractPaymentConfirmationToDoRepository contractPaymentConfirmationToDoRepository;

    public PayTabTransactionJob() {

        payTabsHelper = Setup.getApplicationContext()
                .getBean(PayTabsHelper.class);
        backgroundTaskService = Setup.getApplicationContext()
                .getBean(BackgroundTaskService.class);
        contractPaymentConfirmationToDoRepository = Setup.getRepository(ContractPaymentConfirmationToDoRepository.class);
    }

    @Override
    public void run(Map<String, ?> map) {
        this.checkPayTabTransaction();
    }
    
    @Transactional
    public void checkPayTabTransaction() {
        applyChanges(getData());
    }

    private void applyChanges(List<ContractPaymentConfirmationToDo> todos) {
        if (todos == null || todos.isEmpty()) return;
        
        for (ContractPaymentConfirmationToDo todo : todos) {
            try {
                logger.info("working on todo: " + todo.getId());
                checkTransaction(todo);
                Thread.sleep(1200);
            } catch (Exception e) {
                logger.log(Level.SEVERE, "error while update todo#" + todo.getId());
                logger.log(Level.SEVERE, ExceptionUtils.getStackTrace(e));
            }
        }
    }
    
    private List<ContractPaymentConfirmationToDo> getData() {
        return contractPaymentConfirmationToDoRepository.findForPayTabTransactionJob();
    }

    @Transactional
    public void checkTransaction(ContractPaymentConfirmationToDo todo) throws PaytabsException {
        PaytabsTransactionInfo transInfo = payTabsHelper.getPayTabsTransactionInfo(todo.getCreditCardInfo());

        logger.log(Level.INFO, "result: " + transInfo.getPayment_result().getResponse_status());
        logger.log(Level.INFO, "authorizationCode: " + (transInfo.getPayment_result().getResponse_code() == null ?
                "null" : transInfo.getPayment_result().getResponse_code()));
        // ACC-5587
        if (transInfo.getPayment_result().getResponse_code() != null) {
            todo.setAuthorizationCode(transInfo.getPayment_result().getResponse_code());
            contractPaymentConfirmationToDoRepository.save(todo);
        }

        boolean paidSuccess = transInfo.getPayment_result().getResponse_status().equals("A");
        if (paidSuccess){
            backgroundTaskService.create(new BackgroundTask.builder(
                    "checkTransaction_" + todo.getId(),
                    "accounting",
                    "contractPaymentConfirmationToDoService",
                    "paidViaPayTabs")
                    .withRelatedEntity("ContractPaymentConfirmationToDo", todo.getId())
                    .withParameters(
                            new Class[] { Long.class, Boolean.class, String.class },
                            new Object[] { todo.getId(), true, transInfo.getPayment_result().getResponse_message() })
                    .withQueue(BackgroundTaskQueues.SequentialQueue)
                    .build());
        }
    }
}