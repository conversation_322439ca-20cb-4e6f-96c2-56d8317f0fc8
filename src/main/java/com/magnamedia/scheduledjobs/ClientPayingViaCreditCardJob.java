package com.magnamedia.scheduledjobs;

import com.magnamedia.core.Setup;
import com.magnamedia.core.schedule.MagnamediaJob;
import com.magnamedia.entity.AbstractPaymentTypeConfig;
import com.magnamedia.entity.ContractPaymentTerm;
import com.magnamedia.entity.FlowProcessorEntity;
import com.magnamedia.entity.Payment;
import com.magnamedia.entity.workflow.FlowEventConfig;
import com.magnamedia.entity.workflow.FlowSubEventConfig;
import com.magnamedia.module.type.ContractStatus;
import com.magnamedia.module.type.ContractPaymentTermReason;
import com.magnamedia.module.type.PaymentStatus;
import com.magnamedia.repository.*;
import com.magnamedia.service.*;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
//ACC-4715
public class ClientPayingViaCreditCardJob implements MagnamediaJob {

    private static final Logger logger = Logger.getLogger(ClientPayingViaCreditCardJob.class.getName());
    private FlowEventConfig flowEventConfig;
    private FlowProcessorService flowProcessorService;
    private FlowProcessorEntityRepository flowProcessorEntityRepository;
    private ClientPayingViaCreditCardService clientPayingViaCreditCardService;
    private ContractPaymentTermRepository contractPaymentTermRepository;
    private ContractPaymentTermServiceNew contractPaymentTermServiceNew;

    Integer sendDdSigningOfferDay = 1;

    @Override
    public void run(Map<String, ?> map) {
        logger.log(Level.INFO, "Start job");
        flowProcessorService = Setup.getApplicationContext().getBean(FlowProcessorService.class);
        clientPayingViaCreditCardService = Setup.getApplicationContext().getBean(ClientPayingViaCreditCardService.class);
        contractPaymentTermServiceNew = Setup.getApplicationContext().getBean(ContractPaymentTermServiceNew.class);
        flowProcessorEntityRepository = Setup.getRepository(FlowProcessorEntityRepository.class);
        contractPaymentTermRepository = Setup.getRepository(ContractPaymentTermRepository.class);

        flowEventConfig = Setup.getRepository(FlowEventConfigRepository.class)
                .findByName(FlowEventConfig.FlowEventName.CLIENTS_PAYING_VIA_Credit_Card);

        if (flowEventConfig == null) return;
        logger.log(Level.INFO, "flowEventConfig id: {0}", flowEventConfig.getId());

        if (flowEventConfig.hasTag("day_of_send_dd_signing_offer")) {
            sendDdSigningOfferDay = Integer.parseInt(
                    flowEventConfig.getTagValue("day_of_send_dd_signing_offer").getValue());
        }

        findRecurringPaymentAmountUpdated();

        removePaymentsWhenErpParameterFalse();

        removeTokenFromInactiveCpt();

        createReminderFlow();

        startAutomaticCollectionRecurringCreditCardPayment();

        checkBackgroundTaskFailure();

        sendMessages();

        logger.log(Level.SEVERE, "End job");
    }

    private void removeTokenFromInactiveCpt() {
        logger.info("start");

        Page<ContractPaymentTerm> l;
        Long lastId = -1L;

        do {
            l = contractPaymentTermRepository.findContractPaymentTermWithNotNullSourceIDAndIsActiveFalse(lastId, PageRequest.of(0, 200));
            l.forEach(cpt -> {
                logger.info("cpt id: " + cpt.getId());
                try {
                    ContractPaymentTerm newCpt = cpt.getContract().getActiveContractPaymentTerm();
                    if(newCpt.getSourceId() == null && newCpt.getReason() != null && newCpt.getReason().equals(ContractPaymentTermReason.REPLACEMENT) &&
                            contractPaymentTermRepository.findLastInActiveCPTForContract(cpt.getContract()).get(0).equals(cpt.getId())) {

                        newCpt = contractPaymentTermServiceNew.copyTokenToNewCpt(cpt, newCpt);
                        contractPaymentTermRepository.save(newCpt);

                        // remove Token From Inactive Cpt
                        contractPaymentTermRepository.save(contractPaymentTermServiceNew.removeTokenFromCpt(cpt));
                        return;
                    }

                    if (newCpt.getSourceId() == null) {
                        contractPaymentTermServiceNew.deleteCreditCardToken(cpt);
                    } else {
                        contractPaymentTermRepository.save(contractPaymentTermServiceNew.removeTokenFromCpt(cpt));
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            });

            if (!l.getContent().isEmpty()) {
                lastId = l.getContent().get(l.getContent().size() - 1).getId();
            }

        } while (!l.getContent().isEmpty());

        logger.info("finish");
    }

    public void sendMessages() {
        logger.info("start");

        List<FlowProcessorEntity> flowProcessorEntities = flowProcessorEntityRepository
                .findByFlowEventConfigAndStoppedFalseAndCompletedFalse(flowEventConfig);
        logger.log(Level.SEVERE, "flowProcessorEntityRepository size: {0}", flowProcessorEntities.size());

        for (FlowProcessorEntity entity : flowProcessorEntities) {
            logger.log(Level.INFO, "processing entity id: {0}", entity.getId());

            try {
                ContractPaymentTerm contractPaymentTerm = entity.getContractPaymentTerm();
                logger.log(Level.INFO, "contractPaymentTerms id: {0}", contractPaymentTerm.getId());

                logger.log(Level.INFO, "trail: {0}; currentFlowRun: {1}",
                    new Object[] {entity.getTrials(), entity.getCurrentFlowRun()});

                if (!entity.getCurrentSubEvent().getName().equals(FlowSubEventConfig.FlowSubEventName.DD_SIGNING_OFFER) &&
                        !entity.getCurrentSubEvent().getName().equals(FlowSubEventConfig.FlowSubEventName.DD_Rejection) &&
                        clientPayingViaCreditCardService.validateSkippingFlow(entity)) continue;

                switch (entity.getCurrentSubEvent().getName()) {
                    case MONTHLY_REMINDER:
                    case EXPIRED_CARD:
                        // ACC-6564
                        if (new DateTime().isAfter(new DateTime(entity.getContract().getPaidEndDate()))) {
                            entity.setTrials(entity.getCurrentSubEvent().getMaxTrials() - 1);
                            entity.setReminders(entity.getCurrentSubEvent().getMaxReminders());
                        }
                        break;
                    case DD_SIGNING_OFFER: // ACC-7965
                        if (!entity.getContract().isPayingViaCreditCard()) {
                            logger.info("The flag turned to false -> stop the flow");
                            entity.setStopped(true);
                            flowProcessorEntityRepository.save(entity);
                            continue;
                        }
                }

                // ACC-7549
                if (FlowSubEventConfig.FlowSubEventName.MONTHLY_REMINDER.equals(entity.getCurrentSubEvent().getName()) &&
                        entity.getContract().getScheduledDateOfTermination() != null &&
                        ContractStatus.ACTIVE.equals(entity.getContract().getStatus())) {
                    logger.info("keep it silent while the contract is scheduled for termination");
                    continue;
                }

                flowProcessorService.processFlowSubEventConfig(entity);

            } catch (Exception e) {
                logger.log(Level.SEVERE, "error " + e.getMessage());
                e.printStackTrace();
            }
        }

        logger.info("finish");
    }

    private void createReminderFlow() {
        logger.log(Level.INFO, "start");
        int startMonthlyReminderDay = 8;

        if (flowEventConfig.hasTag("monthly_reminder_paying_cc_start_before_x_days")) {
            startMonthlyReminderDay = Integer.parseInt(
                    flowEventConfig.getTagValue("monthly_reminder_paying_cc_start_before_x_days").getValue());
        }

        Date d = new LocalDate().plusDays(startMonthlyReminderDay).toDate();

        for (ContractPaymentTerm c :
                Setup.getRepository(ContractPaymentTermRepository.class)
                        .findByCptAndFlowProcessEntityAndPayingViaCreditCard(d)) {
            try {
                logger.log(Level.INFO, "cpt id : {0}", c.getId());

                clientPayingViaCreditCardService.startReminderFlow(c,
                        FlowEventConfig.FlowEventName.CLIENTS_PAYING_VIA_Credit_Card,
                        FlowSubEventConfig.FlowSubEventName.MONTHLY_REMINDER,
                        startMonthlyReminderDay);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        logger.info("finish");
    }

    private void startAutomaticCollectionRecurringCreditCardPayment() {
        logger.log(Level.INFO, "start");
        if (!ClientPayingViaCreditCardService.erpAllowedRecurring()) return;

        Page<Payment> payments;
        Long lastId = -1L;

        do {
            payments = Setup.getRepository(PaymentRepository.class)
                    .findByCptAndFlowProcessEntityAndPayingViaCreditCardAndAllowRecurring(
                    lastId,
                    PaymentStatus.PDC,
                    new Date(),
                    FlowProcessorService.recurringFailureFlowsWithExpiredCard,
                    PageRequest.of(0, 200));

            payments.getContent()
                    .forEach(payment -> {
                try {
                    ContractPaymentTerm cpt = payment.getContract().getActiveContractPaymentTerm();

                    logger.log(Level.INFO, "cpt id : {0}", cpt.getId() + "; payment id: " + payment.getId());
                    clientPayingViaCreditCardService.createCaptureRecurringPaymentBGT(payment);

                } catch (Exception e) {
                    e.printStackTrace();
                }
            });

            if (!payments.getContent().isEmpty()) {
                lastId = payments.getContent().get(payments.getContent().size() - 1).getId();
            }

        } while (!payments.getContent().isEmpty());

        sendErrorCodesWithoutSubEventEmail();

        logger.info("finish");
    }

    private void checkBackgroundTaskFailure() {
        logger.info("start");

        clientPayingViaCreditCardService.handleFailedErpCaptureRecurringPayment();

        logger.info("finish");
    }

    private void sendErrorCodesWithoutSubEventEmail() {
        logger.info("start");

        clientPayingViaCreditCardService.sendEmailsForCodeIssueNotRelatedToSubEvent();

        logger.info("finish");
    }

    private void removePaymentsWhenErpParameterFalse() {
        logger.info("start");

        Page<ContractPaymentTerm> l;
        Long lastId = -1L;

        if (ClientPayingViaCreditCardService.erpAllowedRecurring()) return;

        do {

            l = contractPaymentTermRepository.findActiveCPTsWithNotNullSourceId(lastId, PageRequest.of(0, 200));

            l.getContent().forEach(cpt -> {
                try {
                    logger.info("cpt id: " + cpt.getId());
                    contractPaymentTermServiceNew.deleteCreditCardToken(cpt);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            });

            if (!l.getContent().isEmpty()) {
                lastId = l.getContent().get(l.getContent().size() - 1).getId();
            }

        } while (!l.getContent().isEmpty());

        logger.info("finish");
    }

    private void findRecurringPaymentAmountUpdated() {
        logger.info("Start" );

        Page<Map<String, Object>> results;
        Long lastId = -1L;

        do {
            results = Setup.getRepository(PaymentRepository.class).findRecurringPaymentAmountUpdated(
                    lastId,
                    PageRequest.of(0, 200));

            results.getContent().forEach(r -> {
                try {
                    clientPayingViaCreditCardService.removeRecurringFlagAndCreateToDo(
                            (Payment) r.get("payment"),
                            (ContractPaymentTerm) r.get("cpt"));

                } catch (Exception e) {
                    e.printStackTrace();
                }
            });

            if (!results.getContent().isEmpty()) {
                lastId = ((Payment) results.getContent().get(results.getContent().size() - 1).get("payment")).getId();
            }

        } while (!results.getContent().isEmpty());
        logger.info("finish");
    }
}